using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models;

public class RouteConfig
{
    /// <summary>
    /// 监听端口
    /// </summary>
    [Required(ErrorMessage = "监听端口是必需的")]
    [Range(1, int.MaxValue, ErrorMessage = "监听端口必须大于0")]
    [JsonPropertyName("Port")]
    public int Port { get; set; }
    
    /// <summary>
    /// 默认目标设备ID
    /// </summary>
    [Required(ErrorMessage = "默认目标设备ID是必需的")]
    [Range(1, int.MaxValue, ErrorMessage = "默认目标设备ID必须大于0")]
    [JsonPropertyName("DefaultDeviceId")]
    public int DefaultDeviceId { get; set; }
    
    /// <summary>
    /// 转换列表
    /// </summary>
    [JsonPropertyName("Transforms")]
    public List<TransformRout> Transforms { get; set; } = [];
}

/// <summary>
/// 转换路由
/// </summary>
public class TransformRout
{
    /// <summary>
    /// 起始地址
    /// </summary>
    [Range(1, ushort.MaxValue, ErrorMessage = "起始地址必须大于0")]
    [JsonPropertyName("SourceStartAddress")]
    public ushort SourceStartAddress { get; set; }
    
    /// <summary>
    /// 数量
    /// </summary>
    [Range(1, ushort.MaxValue, ErrorMessage = "数量必须大于0")]
    [JsonPropertyName("Quantity")]
    public ushort Quantity { get; set; }
    
    /// <summary>
    /// 目标设备ID
    /// </summary>
    [JsonPropertyName("TargetDeviceId")]
    public int TargetDeviceId { get; set; }
    
    /// <summary>
    /// 从机ID
    /// </summary>
    [Range(1, ushort.MaxValue, ErrorMessage = "从机ID必须大于0")]
    [JsonPropertyName("TargetSlaveId")]
    public ushort TargetSlaveId { get; set; }
    
    /// <summary>
    /// 起始地址
    /// </summary>
    [Range(1, ushort.MaxValue, ErrorMessage = "起始地址必须大于0")]
    [JsonPropertyName("TargetStartAddress")]
    public ushort TargetStartAddress { get; set; }
    
    /// <summary>
    /// 功能码
    /// </summary>
    [MinLength(1, ErrorMessage = "功能码是必需的")]
    [JsonPropertyName("FunctionCodes")]
    [UnconditionalSuppressMessage("Trimming", "IL2026:Members annotated with 'RequiresUnreferencedCodeAttribute' require dynamic access otherwise can break functionality when trimming application code", Justification = "<Pending>")]
    public List<ushort> FunctionCodes { get; set; } = []; 
}