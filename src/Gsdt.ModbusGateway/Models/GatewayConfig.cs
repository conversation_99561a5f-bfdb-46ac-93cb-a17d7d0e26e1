using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models;

[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class GatewayConfig
{
    [JsonIgnore]
    [Required(ErrorMessage = "监听端口是必需的")]
    [MinLength(1, ErrorMessage = "至少需要配置一个监听端口")]
    [UnconditionalSuppressMessage("Trimming",
        "IL2026:Members annotated with 'RequiresUnreferencedCodeAttribute' require dynamic access otherwise can break functionality when trimming application code",
        Justification = "<Pending>")]
    public List<int> ListenPorts => Routes.Select(r => r.Port).ToList();

    [JsonIgnore]
    [Required(ErrorMessage = "目标设备列表是必需的")]
    [MinLength(1, ErrorMessage = "至少需要配置一个目标设备")]
    [UnconditionalSuppressMessage("Trimming",
        "IL2026:Members annotated with 'RequiresUnreferencedCodeAttribute' require dynamic access otherwise can break functionality when trimming application code",
        Justification = "<Pending>")]
    public List<TargetDevice> TargetDevices
    {
        get
        {
            var devices = new List<TargetDevice>();
            devices.AddRange(TcpTargetDevices);
            devices.AddRange(RtuTargetDevices);
            return devices;
        }
    }

    /// <summary>
    /// TCP目标设备列表
    /// </summary>
    [JsonPropertyName("TcpTargetDevices")]
    public List<TcpTargetDevice> TcpTargetDevices { get; set; } = [];

    /// <summary>
    /// RTU目标设备列表
    /// </summary>
    [JsonPropertyName("RtuTargetDevices")]
    public List<RtuTargetDevice> RtuTargetDevices { get; set; } = [];

    /// <summary>
    /// 路由映射列表
    /// </summary>
    [JsonPropertyName("Routes")]
    [Required(ErrorMessage = "路由映射列表是必需的")]
    [MinLength(1, ErrorMessage = "至少需要配置一个路由映射")]
    [UnconditionalSuppressMessage("Trimming",
        "IL2026:Members annotated with 'RequiresUnreferencedCodeAttribute' require dynamic access otherwise can break functionality when trimming application code",
        Justification = "<Pending>")]
    public List<RouteConfig> Routes { get; set; } = [];
}