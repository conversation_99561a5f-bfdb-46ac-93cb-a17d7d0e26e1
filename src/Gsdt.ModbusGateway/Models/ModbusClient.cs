using System.Collections.Concurrent;
using NModbus;

namespace Gsdt.ModbusGateway.Models;

public class ModbusClient : IDisposable
{
    private TimeSpan _lastReadCoilsTime = TimeSpan.Zero;
    private TimeSpan _lastDiscreteInputsTime = TimeSpan.Zero;
    private TimeSpan _lastReadHoldingRegistersTime = TimeSpan.Zero;
    private TimeSpan _lastInputRegistersTime = TimeSpan.Zero;
    private ConcurrentDictionary<string, Memory<byte>> ReadCache { get; } = new();
    
    protected IModbusMaster? Master { get; set; }
    
    /// <summary>
    /// 获取或设置超时时间
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(5);

    /// <summary>
    /// 读取线圈
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="quantity">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>线圈值</returns>
    public virtual async Task<Memory<byte>> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity,
        CancellationToken cancellationToken)
    {
        var key = $"ReadCoilsAsync_{slaveId}_{startAddress}_{quantity}";
        if(ReadCache.TryGetValue(key, out var cachedValue) && (DateTime.Now - _lastReadCoilsTime).Millisecond < 5)
        {
            Console.WriteLine($"3 读取保持寄存器缓存 ReadHoldingRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
            return cachedValue;
        }
        
        Console.WriteLine($"1 读取线圈 ReadCoilsAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        var coils = await Task.Run(() => Master.ReadCoils(slaveId, startAddress, quantity), cancellationToken);

        // 将布尔数组转换为字节数组
        var byteCount = (quantity + 7) / 8;
        var result = new byte[byteCount];

        for (var i = 0; i < quantity; i++)
        {
            if (coils[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                result[byteIndex] |= (byte)(1 << bitIndex);
            }
        }
        
        ReadCache[key] = result;
        _lastReadCoilsTime = TimeSpan.FromTicks(DateTime.Now.Ticks);

        return result;
    }

    /// <summary>
    /// 读取离散输入
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="quantity">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>离散输入值</returns>
    public virtual async Task<Memory<byte>> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity,
        CancellationToken cancellationToken)
    {
        var key = $"ReadDiscreteInputsAsync_{slaveId}_{startAddress}_{quantity}";
        if(ReadCache.TryGetValue(key, out var cachedValue) && (DateTime.Now - _lastDiscreteInputsTime).Millisecond < 5)
        {
            Console.WriteLine($"3 读取保持寄存器缓存 ReadHoldingRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
            return cachedValue;
        }
        
        Console.WriteLine($"2 读取离散输入 ReadDiscreteInputsAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        var inputs = await Task.Run(() => Master.ReadInputs(slaveId, startAddress, quantity), cancellationToken);

        // 将布尔数组转换为字节数组
        var byteCount = (quantity + 7) / 8;
        var result = new byte[byteCount];

        for (var i = 0; i < quantity; i++)
        {
            if (inputs[i])
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                result[byteIndex] |= (byte)(1 << bitIndex);
            }
        }
        
        ReadCache[key] = result;
        _lastDiscreteInputsTime = TimeSpan.FromTicks(DateTime.Now.Ticks);

        return result;
    }

    /// <summary>
    /// 读取保持寄存器
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="quantity">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>保持寄存器值</returns>
    public virtual async Task<Memory<byte>> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity,
        CancellationToken cancellationToken)
    {
        var key = $"ReadHoldingRegistersAsync_{slaveId}_{startAddress}_{quantity}";
        if(ReadCache.TryGetValue(key, out var cachedValue) && (DateTime.Now - _lastReadHoldingRegistersTime).Millisecond < 5)
        {
            Console.WriteLine($"3 读取保持寄存器缓存 ReadHoldingRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
            return cachedValue;
        }
        
        Console.WriteLine($"3 读取保持寄存器 ReadHoldingRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        var registers = await Task.Run(() => Master.ReadHoldingRegisters(slaveId, startAddress, quantity), cancellationToken);

        // 将ushort数组转换为字节数组
        var result = new byte[quantity * 2];

        if (registers.Length == 0)
        {
            return result;
        }

        for (var i = 0; i < quantity; i++)
        {
            result[i * 2] = (byte)(registers[i] >> 8);
            result[i * 2 + 1] = (byte)(registers[i] & 0xFF);
        }
        
        ReadCache[key] = result;
        _lastReadHoldingRegistersTime = TimeSpan.FromTicks(DateTime.Now.Ticks);

        return result;
    }

    /// <summary>
    /// 读取输入寄存器
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="quantity">数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>输入寄存器值</returns>
    public virtual async Task<Memory<byte>> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity,
        CancellationToken cancellationToken)
    {
        var key = $"ReadInputRegistersAsync{slaveId}_{startAddress}_{quantity}";
        if(ReadCache.TryGetValue(key, out var cachedValue) && (DateTime.Now - _lastInputRegistersTime).Millisecond < 5)
        {
            Console.WriteLine($"3 读取保持寄存器缓存 ReadHoldingRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
            return cachedValue;
        }
        
        Console.WriteLine($"4 读取输入寄存器 ReadInputRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, quantity={quantity}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        var registers = await Task.Run(() => Master.ReadInputRegisters(slaveId, startAddress, quantity),
            cancellationToken);

        // 将ushort数组转换为字节数组
        var result = new byte[quantity * 2];

        for (var i = 0; i < quantity; i++)
        {
            result[i * 2] = (byte)(registers[i] >> 8);
            result[i * 2 + 1] = (byte)(registers[i] & 0xFF);
        }
        
        ReadCache[key] = result;
        _lastInputRegistersTime = TimeSpan.FromTicks(DateTime.Now.Ticks);

        return result;
    }

    /// <summary>
    /// 写入单个线圈
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="address">地址</param>
    /// <param name="value">值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public virtual async Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value,
        CancellationToken cancellationToken)
    {
        Console.WriteLine($"5 写入单个线圈 ReadInputRegistersAsync: slaveId={slaveId}, address={address}, value={value}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        await Task.Run(() => Master.WriteSingleCoil(slaveId, address, value), cancellationToken);
        
        ClearReadCache();
    }

    /// <summary>
    /// 写入单个寄存器
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="address">地址</param>
    /// <param name="value">值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public virtual async Task WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value,
        CancellationToken cancellationToken)
    {
        Console.WriteLine($"6 写入单个寄存器 WriteSingleRegisterAsync: slaveId={slaveId}, address={address}, value={value}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        await Task.Run(() => Master.WriteSingleRegister(slaveId, address, value), cancellationToken);
        
        ClearReadCache();
    }

    /// <summary>
    /// 写入多个线圈
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="values">值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public virtual async Task WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values,
        CancellationToken cancellationToken)
    {
        Console.WriteLine($"15 写入多个线圈 WriteMultipleCoilsAsync: slaveId={slaveId}, startAddress={startAddress}, value={values}");
        
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        await Task.Run(() => Master.WriteMultipleCoils(slaveId, startAddress, values), cancellationToken);
        
        ClearReadCache();
    }

    /// <summary>
    /// 写入多个寄存器
    /// </summary>
    /// <param name="slaveId">从站ID</param>
    /// <param name="startAddress">起始地址</param>
    /// <param name="values">值</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public virtual async Task WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values,
        CancellationToken cancellationToken)
    {
        Console.WriteLine($"16 写入多个寄存器 WriteMultipleRegistersAsync: slaveId={slaveId}, startAddress={startAddress}, value={values}");
        if (Master == null)
        {
            throw new InvalidOperationException("客户端未连接");
        }

        await Task.Run(() => Master.WriteMultipleRegisters(slaveId, startAddress, values), cancellationToken);

        ClearReadCache();
    }

    protected void Dispose(bool disposing)
    {
        if (disposing)
        {
            Master?.Dispose();
        }
    }

    public virtual void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 清除缓存
    /// </summary>
    private void ClearReadCache()
    {
        ReadCache.Clear();
    }
}