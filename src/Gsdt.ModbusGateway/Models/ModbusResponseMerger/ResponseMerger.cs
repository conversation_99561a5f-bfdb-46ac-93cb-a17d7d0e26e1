namespace Gsdt.ModbusGateway.Models.ModbusResponseMerger;

public interface IResponseMerger
{
    byte[] Merge(List<ModbusResponse> subResponses, byte[] originalRequest);
}

public abstract  class ResponseMergerBase  : IResponseMerger
{
    protected static byte[] BuildFinalResponse(byte functionCode, byte[] data, byte[] originalRequest)
    {
        // 构建PDU (Protocol Data Unit): 功能码 + 数据
        var pdu = new List<byte> { functionCode };
        pdu.AddRange(data);

        // 构建完整的Modbus TCP响应
        var final = new List<byte>();

        // 复制事务ID (2字节) 和 协议ID (2字节)
        final.AddRange(originalRequest.Take(4));

        // 计算并设置长度字段 (PDU长度 + 单元标识符长度)
        var length = (ushort)(pdu.Count + 1); // +1 是单元标识符的长度
        final.Add((byte)(length >> 8));    // 长度高字节
        final.Add((byte)(length & 0xFF));  // 长度低字节

        // 添加单元标识符 (从原始请求中获取)
        final.Add(originalRequest[6]);

        // 添加PDU
        final.AddRange(pdu);

        return final.ToArray();
    }

    public abstract byte[] Merge(List<ModbusResponse> subResponses, byte[] originalRequest);
}


/// <summary>
/// 0x01 Read Coils
/// </summary>
public class ReadCoilsMerger : ResponseMergerBase
{
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        // 从原始请求中解析起始地址和位数量
        var startAddress = (ushort)((originalRequest[8] << 8) | originalRequest[9]);
        var bitCount = (ushort)((originalRequest[10] << 8) | originalRequest[11]);
        var byteCount = (bitCount + 7) / 8; // 计算需要的字节数
        var merged = new byte[byteCount]; // 创建结果数组

        // 按照源地址排序，确保按顺序处理
        foreach (var res in responses.OrderBy(r => r.SourceStartAddress))
        {
            // 计算此响应数据在最终结果中的位偏移量
            var offsetBits = (res.SourceStartAddress - startAddress);

            // 确保偏移量有效
            if (offsetBits < 0)
            {
                throw new InvalidOperationException($"Invalid source address {res.SourceStartAddress}, less than start address {startAddress}");
            }

            // 从响应中提取数据部分
            // Modbus TCP响应格式: MBAP头(7字节) + 功能码(1字节) + 字节数(1字节) + 数据
            if (res.Data.Length < 9)
            {
                throw new InvalidOperationException($"Response data too short: {res.Data.Length} bytes");
            }

            var byteCountInResponse = res.Data[8]; // 响应中的字节数
            var data = res.Data.Skip(9).Take(byteCountInResponse).ToArray(); // 提取实际数据

            // 处理每个字节中的每一位
            for (var i = 0; i < data.Length; i++)
            {
                for (var b = 0; b < 8; b++)
                {
                    // 计算全局位置
                    var globalBitPos = offsetBits + (i * 8) + b;

                    // 确保不超出请求的位数
                    if (globalBitPos >= bitCount) break;

                    // 检查位值
                    var value = (data[i] & (1 << b)) != 0;

                    // 如果位为1，设置结果中对应的位
                    if (value)
                        merged[globalBitPos / 8] |= (byte)(1 << (globalBitPos % 8));
                }
            }
        }

        // 构建最终响应
        var result = new List<byte> { (byte)merged.Length }; // 字节数
        result.AddRange(merged); // 数据
        return BuildFinalResponse(0x01, result.ToArray(), originalRequest);
    }
}

/// <summary>
/// 0x02 Read Discrete Inputs
/// </summary>
public class ReadDiscreteInputsMerger : ReadCoilsMerger
{
    /* 继承自ReadCoilsMerger，处理逻辑相同，但使用不同的功能码 */
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        // 使用基类的合并逻辑，但使用功能码0x02
        var baseResult = base.Merge(responses, originalRequest);

        // 提取原始数据部分（跳过MBAP头和功能码）
        var dataStartIndex = 8; // MBAP头(7字节) + 功能码(1字节)
        var data = baseResult.Skip(dataStartIndex).ToArray();

        // 使用功能码0x02重新构建响应
        return BuildFinalResponse(0x02, data, originalRequest);
    }
}

/// <summary>
/// 0x03 Read Holding Registers
/// </summary>
public class ReadHoldingRegistersMerger : ResponseMergerBase
{
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        // 从原始请求中解析起始地址和寄存器数量
        var startAddress = (ushort)((originalRequest[8] << 8) | originalRequest[9]);
        var registerCount = (ushort)((originalRequest[10] << 8) | originalRequest[11]);
        var merged = new byte[registerCount * 2]; // 每个寄存器是 2 字节

        // 按照源地址排序，确保按顺序处理
        foreach (var res in responses.OrderBy(r => r.SourceStartAddress))
        {
            // 计算此响应数据在最终结果中的字节偏移量
            var offset = (res.SourceStartAddress - startAddress) * 2; // 每个寄存器是 2 字节

            // 确保偏移量有效
            if (offset < 0)
            {
                throw new InvalidOperationException($"Invalid source address {res.SourceStartAddress}, less than start address {startAddress}");
            }

            // 从响应中提取数据部分
            // Modbus TCP响应格式: MBAP头(7字节) + 功能码(1字节) + 字节数(1字节) + 数据
            if (res.Data.Length < 9)
            {
                throw new InvalidOperationException($"Response data too short: {res.Data.Length} bytes");
            }

            var byteCountInResponse = res.Data[8]; // 响应中的字节数
            var data = res.Data.Skip(9).Take(byteCountInResponse).ToArray(); // 提取实际数据

            // 确保不超出目标数组范围
            if (offset + data.Length > merged.Length)
            {
                throw new InvalidOperationException($"Data from address {res.SourceStartAddress} exceeds the requested register range");
            }

            // 复制数据到结果数组
            Buffer.BlockCopy(data, 0, merged, offset, data.Length);
        }

        // 构建最终响应
        var result = new List<byte> { (byte)(merged.Length) }; // 字节数
        result.AddRange(merged); // 数据
        return BuildFinalResponse(0x03, result.ToArray(), originalRequest);
    }
}

/// <summary>
/// 0x04 Read Input Registers
/// </summary>
public class ReadInputRegistersMerger : ReadHoldingRegistersMerger
{
    /* 继承自ReadHoldingRegistersMerger，处理逻辑相同，但使用不同的功能码 */
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        // 使用基类的合并逻辑，但使用功能码0x04
        var baseResult = base.Merge(responses, originalRequest);

        // 提取原始数据部分（跳过MBAP头和功能码）
        var dataStartIndex = 8; // MBAP头(7字节) + 功能码(1字节)
        var data = baseResult.Skip(dataStartIndex).ToArray();

        // 使用功能码0x04重新构建响应
        return BuildFinalResponse(0x04, data, originalRequest);
    }
}

/// <summary>
/// 0x05 Write Single Coil
/// </summary>
public class WriteSingleCoilMerger : ResponseMergerBase
{
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        if (responses.Count == 0)
        {
            throw new InvalidOperationException("No responses to merge");
        }

        // 写单个线圈的响应应该与请求的地址和值部分相同
        // 从原始请求中提取地址和值
        var address = new[] { originalRequest[8], originalRequest[9] }; // 地址部分
        var value = new[] { originalRequest[10], originalRequest[11] }; // 值部分

        // 验证所有响应与请求一致
        foreach (var res in responses)
        {
            // 确保响应数据长度足够
            if (res.Data.Length < 12) // MBAP头(7) + 功能码(1) + 地址(2) + 值(2)
            {
                throw new InvalidOperationException($"Response data too short: {res.Data.Length} bytes");
            }

            // 提取响应中的地址和值
            var respAddress = new[] { res.Data[8], res.Data[9] };
            var respValue = new[] { res.Data[10], res.Data[11] };

            // 验证地址和值是否一致
            if (!respAddress.SequenceEqual(address) || !respValue.SequenceEqual(value))
            {
                throw new InvalidOperationException($"Invalid write response at {res.SourceStartAddress}: address or value mismatch");
            }
        }

        // 构建响应数据（地址+值）
        var responseData = new List<byte>();
        responseData.AddRange(address); // 地址
        responseData.AddRange(value);   // 值

        // 返回最终响应
        return BuildFinalResponse(0x05, responseData.ToArray(), originalRequest);
    }
}

/// <summary>
/// 0x06 Write Single Register
/// </summary>
public class WriteSingleRegisterMerger : ResponseMergerBase
{
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        if (responses.Count == 0)
        {
            throw new InvalidOperationException("No responses to merge");
        }

        // 写单个寄存器的响应应该与请求的地址和值部分相同
        // 从原始请求中提取地址和值
        var address = new[] { originalRequest[8], originalRequest[9] }; // 地址部分
        var value = new[] { originalRequest[10], originalRequest[11] }; // 值部分

        // 验证所有响应与请求一致
        foreach (var res in responses)
        {
            // 确保响应数据长度足够
            if (res.Data.Length < 12) // MBAP头(7) + 功能码(1) + 地址(2) + 值(2)
            {
                throw new InvalidOperationException($"Response data too short: {res.Data.Length} bytes");
            }

            // 提取响应中的地址和值
            var respAddress = new[] { res.Data[8], res.Data[9] };
            var respValue = new[] { res.Data[10], res.Data[11] };

            // 验证地址和值是否一致
            if (!respAddress.SequenceEqual(address) || !respValue.SequenceEqual(value))
            {
                throw new InvalidOperationException($"Invalid write response at {res.SourceStartAddress}: address or value mismatch");
            }
        }

        // 构建响应数据（地址+值）
        var responseData = new List<byte>();
        responseData.AddRange(address); // 地址
        responseData.AddRange(value);   // 值

        // 返回最终响应
        return BuildFinalResponse(0x06, responseData.ToArray(), originalRequest);
    }
}

/// <summary>
/// 0x0F Write Multiple Coils
/// </summary>
public class WriteMultipleCoilsMerger : ResponseMergerBase
{
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        if (responses.Count == 0)
        {
            throw new InvalidOperationException("No responses to merge");
        }

        // 从原始请求中提取起始地址和数量
        var startAddress = (ushort)((originalRequest[8] << 8) | originalRequest[9]);
        var quantity = (ushort)((originalRequest[10] << 8) | originalRequest[11]);

        // 验证所有子请求地址连续且总数匹配
        var currentAddress = startAddress;
        foreach (var res in responses.OrderBy(r => r.SourceStartAddress))
        {
            // 确保响应数据长度足够
            if (res.Data.Length < 12) // MBAP头(7) + 功能码(1) + 地址(2) + 数量(2)
            {
                throw new InvalidOperationException($"Response data too short: {res.Data.Length} bytes");
            }

            // 验证地址连续性
            if (res.SourceStartAddress != currentAddress)
            {
                throw new InvalidDataException($"Address gap at {res.SourceStartAddress}, expected {currentAddress}");
            }

            // 从响应中提取数量
            var count = (ushort)((res.Data[10] << 8) | res.Data[11]);
            currentAddress += count;
        }

        // 验证总数量是否匹配
        if (currentAddress - startAddress != quantity)
        {
            throw new InvalidDataException($"Total quantity mismatch: got {currentAddress - startAddress}, expected {quantity}");
        }

        // 构建响应数据（地址+数量）
        var responseData = new List<byte>
        {
            originalRequest[8], // 地址高字节
            originalRequest[9], // 地址低字节
            originalRequest[10], // 数量高字节
            originalRequest[11] // 数量低字节
        };

        // 返回最终响应
        return BuildFinalResponse(0x0F, responseData.ToArray(), originalRequest);
    }
}

/// <summary>
/// 0x10 Write Multiple Registers
/// </summary>
public class WriteMultipleRegistersMerger : ResponseMergerBase
{
    public override byte[] Merge(List<ModbusResponse> responses, byte[] originalRequest)
    {
        if (responses.Count == 0)
        {
            throw new InvalidOperationException("No responses to merge");
        }

        // 从原始请求中提取起始地址和数量
        var startAddress = (ushort)((originalRequest[8] << 8) | originalRequest[9]);
        var quantity = (ushort)((originalRequest[10] << 8) | originalRequest[11]);

        // 验证所有子请求地址连续且总数匹配
        var currentAddress = startAddress;
        foreach (var res in responses.OrderBy(r => r.SourceStartAddress))
        {
            // 确保响应数据长度足够
            if (res.Data.Length < 12) // MBAP头(7) + 功能码(1) + 地址(2) + 数量(2)
            {
                throw new InvalidOperationException($"Response data too short: {res.Data.Length} bytes");
            }

            // 验证地址连续性
            if (res.SourceStartAddress != currentAddress)
            {
                throw new InvalidDataException($"Address gap at {res.SourceStartAddress}, expected {currentAddress}");
            }

            // 从响应中提取数量
            var count = (ushort)((res.Data[10] << 8) | res.Data[11]);
            currentAddress += count;
        }

        // 验证总数量是否匹配
        if (currentAddress - startAddress != quantity)
        {
            throw new InvalidDataException($"Total quantity mismatch: got {currentAddress - startAddress}, expected {quantity}");
        }

        // 构建响应数据（地址+数量）
        var responseData = new List<byte>
        {
            originalRequest[8], // 地址高字节
            originalRequest[9], // 地址低字节
            originalRequest[10], // 数量高字节
            originalRequest[11] // 数量低字节
        };

        // 返回最终响应
        return BuildFinalResponse(0x10, responseData.ToArray(), originalRequest);
    }
}