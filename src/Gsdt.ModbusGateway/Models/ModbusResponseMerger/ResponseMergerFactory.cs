namespace Gsdt.ModbusGateway.Models.ModbusResponseMerger;


/// <summary>
/// 策略工厂
/// </summary>
public class ResponseMergerFactory
{
    private static readonly Dictionary<byte, IResponseMerger> Strategies = new()
    {
        [0x01] = new ReadCoilsMerger(),
        [0x02] = new ReadDiscreteInputsMerger(),
        [0x03] = new ReadHoldingRegistersMerger(),
        [0x04] = new ReadInputRegistersMerger(),
        [0x05] = new WriteSingleCoilMerger(),
        [0x06] = new WriteSingleRegisterMerger(),
        [0x0F] = new WriteMultipleCoilsMerger(),
        [0x10] = new WriteMultipleRegistersMerger()
    };

    public static IResponseMerger GetMerger(byte functionCode) => 
        Strategies.TryGetValue(functionCode, out var merger) 
            ? merger 
            : throw new NotSupportedException($"Function code 0x{functionCode:X2} not supported");
}