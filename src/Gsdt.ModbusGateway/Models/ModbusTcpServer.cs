using System.Collections.Concurrent;
using System.IO.Ports;
using System.Net;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using NetCoreServer;
using NModbus;

namespace Gsdt.ModbusGateway.Models;

/// <summary>
/// Tcp服务
/// </summary>
public class ModbusTcpServer : TcpServer
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly GatewayConfig _config;
    private readonly CancellationTokenSource _stoppingCts;

    public ModbusTcpServer(IPAddress address, int port,
        IModbusRequestHandler modbusRequestHandler, ILogger<ModbusTcpGatewayService> logger,
        GatewayConfig config,
        ThreadSafeModbusClientPool clientPool,
        CancellationTokenSource stoppingCts) : base(address, port)
    {
        _logger = logger;
        _config = config;
        _modbusRequestHandler = modbusRequestHandler;
        _clientPool = clientPool;
        _stoppingCts = stoppingCts;
    }

    protected override TcpSession CreateSession() =>
        new ModbusTcpSession(this, _logger, _modbusRequestHandler, _clientPool, _config, _stoppingCts);
}

public class ModbusTcpSession : TcpSession
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly GatewayConfig _config;
    private readonly CancellationTokenSource _stoppingCts;

    public ModbusTcpSession(
        TcpServer server,
        ILogger<ModbusTcpGatewayService> logger,
        IModbusRequestHandler modbusRequestHandler,
        ThreadSafeModbusClientPool clientPool,
        GatewayConfig config,
        CancellationTokenSource stoppingCts) : base(server)
    {
        _logger = logger;
        _modbusRequestHandler = modbusRequestHandler;
        _clientPool = clientPool;
        _config = config;
        _stoppingCts = stoppingCts;
    }

    protected override void OnReceived(byte[] buffer, long offset, long size)
    {
        // 创建请求的副本
        var request = new byte[size];
        Array.Copy(buffer, request, size);

        // 异步处理请求但不等待
        _ = ProcessRequestAsync(request).ContinueWith(t =>
        {
            if (t.IsFaulted)
            {
                _logger.LogError(t.Exception, $"处理来自 {Server.Address}:{Server.Port} 的请求时发生未处理的异常");
            }
        }, TaskScheduler.Current);
    }

    private async Task ProcessRequestAsync(byte[] request)
    {
        // 获取从站ID（第7个字节，因为Modbus TCP有6字节的MBAP头）
        var slaveId = request[6];

        // 获取功能码（第8个字节）
        var functionCode = request[7];

        // 获取起始地址（第9和10个字节）
        var startAddress = (ushort)((request[8] << 8) | request[9]);

        // 获取数量（第11和12个字节）
        var quantity = (ushort)((request[10] << 8) | request[11]);

        _logger.LogInformation(
            "收到请求：Port={Port}, FunctionCode={FunctionCode}, StartAddress={startAddress}, Quantity={quantity}",
            Server.Port, functionCode, startAddress, quantity);

        try
        {
            // 查找对应的路由配置
            var port = Server.Port;
            var routeConfig = _config.Routes.FirstOrDefault(r => r.Port == port);
            if (routeConfig == null)
            {
                _logger.LogWarning("未找到端口为 {Port} 的路由配置", port);
                throw new InvalidModbusRequestException($"未找到端口为 {port} 的路由配置",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            // 根据路由转换成请求列表
            var requests = TransformRequest(request, routeConfig);
            if (requests.Count == 0)
            {
                _logger.LogWarning("未找到起始地址为 {StartAddress} 的转换配置", startAddress);
                throw new InvalidModbusRequestException($"未找到起始地址为 {startAddress} 的转换配置",
                    (byte)ModbusExceptionCode.GatewayPathUnavailable);
            }

            var responseList = new List<ModbusResponse>();
            foreach (var modbusRequest in requests)
            {
                _logger.LogInformation(
                    $"转发请求到: DeviceId={modbusRequest.DeviceId}, FunctionCode={functionCode}, SlaveId={modbusRequest.SlaveId}, StartAddress={modbusRequest.StartAddress}, Quantity={modbusRequest.Quantity}");
                // 使用线程安全的方式处理请求
                var data = await ProcessThreadSafeRequestAsync(modbusRequest, request, slaveId);
                responseList.Add(new ModbusResponse
                {
                    Data = data,
                    SourceStartAddress = modbusRequest.SourceStartAddress
                });
            }

            // 合并响应报文
            var response = await _modbusRequestHandler.MergeResponsesAsync(request, responseList);
            SendAsync(response);
        }
        catch (InvalidModbusRequestException ex)
        {
            SendExceptionResponse(request, slaveId, ex.ExceptionCode);
        }
        catch (SlaveException ex)
        {
            _logger.LogError(ex, "目标设备返回错误: {Message},功能码={FunctionCode}, 错误码={ExceptionCode}", ex.Message,
                ex.FunctionCode, ex.SlaveExceptionCode);
            SendExceptionResponse(request, slaveId, (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "处理Modbus请求时发生错误");
            SendExceptionResponse(request, slaveId, (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
        }
    }

    private void SendExceptionResponse(byte[] request, byte originalSlaveId, byte exceptionCode)
    {
        var response = ModbusRequestHandler.CreateExceptionResponse(request, originalSlaveId, exceptionCode);
        SendAsync(response);
    }

    /// <summary>
    /// 修改Modbus TCP请求报文的起始地址和寄存器数量
    /// </summary>
    /// <param name="originalRequest">原始请求字节数组</param>
    /// <param name="newSlaveId">新的从机地址</param>
    /// <param name="newStartAddress">新的起始地址</param>
    /// <param name="newQuantity">新的寄存器数量</param>
    /// <returns>修改后的请求字节数组</returns>
    private static byte[] ModifyRequest(byte[] originalRequest, ushort newSlaveId, ushort newStartAddress,
        ushort newQuantity)
    {
        // 复制原始请求以避免修改原数组
        var modifiedRequest = (byte[])originalRequest.Clone();

        // Modbus TCP请求中各字段的偏移位置
        const int slaveIdOffset = 6; // 从机地址（Unit Identifier）
        const int functionCodeOffset = 7; // 功能码
        const int startAddressOffset = 8; // 起始地址
        const int quantityOffset = 10; // 寄存器数量

        // 验证请求长度是否足够
        if (modifiedRequest.Length < 12)
        {
            throw new ArgumentException("Invalid Modbus TCP request length");
        }

        // 修改从机地址（1字节）
        modifiedRequest[slaveIdOffset] = (byte)newSlaveId;

        // 修改起始地址（大端序）
        modifiedRequest[startAddressOffset] = (byte)(newStartAddress >> 8);
        modifiedRequest[startAddressOffset + 1] = (byte)(newStartAddress & 0xFF);

        if (modifiedRequest[functionCodeOffset] < 5)
        {
            // 修改寄存器数量（大端序）
            modifiedRequest[quantityOffset] = (byte)(newQuantity >> 8);
            modifiedRequest[quantityOffset + 1] = (byte)(newQuantity & 0xFF);
        }

        return modifiedRequest;
    }

    /// <summary>
    /// 根据原始请求和路由配置转换成请求列表
    /// </summary>
    /// <param name="request">原始Modbus TCP请求</param>
    /// <param name="route">路由配置</param>
    /// <returns>转换后的请求列表</returns>
    private static List<ModbusRequest> TransformRequest(
        byte[] request,
        RouteConfig route)
    {
        // 从请求中提取必要信息
        var originalSlaveId = request[6];
        var functionCode = request[7];
        var originalStart = (ushort)((request[8] << 8) | request[9]);
        var originalQuantity = (ushort)((request[10] << 8) | request[11]);

        var requests = new List<ModbusRequest>();

        // 如果是写入寄存器，则直接转到目标地址；
        if (functionCode >= 5)
        {
            var transform = route.Transforms.FirstOrDefault(t => t.FunctionCodes.Contains(functionCode) && t.SourceStartAddress == originalStart);
            requests.Add(transform == null
                ? CreateDefaultRequest(route.DefaultDeviceId, originalSlaveId, originalStart, originalQuantity)
                : CreateDefaultRequest(transform.TargetDeviceId, transform.TargetSlaveId, transform.TargetStartAddress, originalQuantity));
            return requests;
        }

        // 根据路由配置转换请求
        var originalEnd = (ushort)(originalStart + originalQuantity - 1);
        var currentPosition = originalStart;
        var remaining = originalQuantity;
        var transforms = route.Transforms.OrderBy(t => t.SourceStartAddress).ToList();

        foreach (var transform in transforms)
        {
            if (!transform.FunctionCodes.Contains(functionCode))
            {
                continue;
            }

            if (remaining <= 0) break;

            var transformStart = transform.SourceStartAddress;
            var transformEnd = (ushort)(transform.SourceStartAddress + transform.Quantity - 1);
            var overlapStart = Math.Max(currentPosition, transformStart);
            var overlapEnd = Math.Min(originalEnd, transformEnd);

            // 处理前置未覆盖部分
            if (currentPosition < overlapStart)
            {
                var defaultQuantity = Math.Min((ushort)(overlapStart - currentPosition), originalQuantity);
                requests.Add(CreateDefaultRequest(
                    route.DefaultDeviceId,
                    originalSlaveId,
                    currentPosition,
                    defaultQuantity));

                currentPosition += defaultQuantity;
                remaining -= defaultQuantity;
            }

            // 处理覆盖部分
            if (overlapStart <= overlapEnd && remaining > 0)
            {
                var transformQuantity = (ushort)(overlapEnd - overlapStart + 1);
                requests.Add(CreateTransformRequest(
                    transform,
                    overlapStart,
                    transformQuantity));

                currentPosition += transformQuantity;
                remaining -= transformQuantity;
            }
        }

        // 处理剩余部分
        if (remaining > 0)
        {
            requests.Add(CreateDefaultRequest(
                route.DefaultDeviceId,
                originalSlaveId,
                currentPosition,
                remaining));
        }

        return requests;
    }

    private static ModbusRequest CreateDefaultRequest(int deviceId, ushort slaveId, ushort start, ushort quantity)
    {
        return new ModbusRequest
        {
            DeviceId = deviceId,
            SlaveId = slaveId,
            StartAddress = start,
            Quantity = quantity,
            SourceStartAddress = start
        };
    }

    private static ModbusRequest CreateTransformRequest(TransformRout transform, ushort overlapStart, ushort quantity)
    {
        return new ModbusRequest
        {
            DeviceId = transform.TargetDeviceId,
            SlaveId = transform.TargetSlaveId,
            StartAddress = (ushort)(transform.TargetStartAddress + (overlapStart - transform.SourceStartAddress)),
            Quantity = quantity,
            SourceStartAddress = transform.SourceStartAddress
        };
    }

    /// <summary>
    /// 使用线程安全的方式处理请求
    /// </summary>
    /// <param name="modbusRequest">Modbus请求</param>
    /// <param name="originalRequest">原始请求数据</param>
    /// <param name="originalSlaveId">原始从站ID</param>
    /// <returns>响应数据</returns>
    private async Task<byte[]> ProcessThreadSafeRequestAsync(ModbusRequest modbusRequest, byte[] originalRequest, byte originalSlaveId)
    {
        // 查找对应的目标设备
        var targetDevice = _config.TargetDevices.FirstOrDefault(d => d.Id == modbusRequest.DeviceId);
        if (targetDevice == null)
        {
            _logger.LogWarning("未找到ID为 {DeviceId} 的目标设备", modbusRequest.DeviceId);
            throw new InvalidModbusRequestException($"未找到ID为 {modbusRequest.DeviceId} 的目标设备",
                (byte)ModbusExceptionCode.GatewayPathUnavailable);
        }

        // 获取线程安全的客户端包装器
        var clientWrapper = _clientPool.GetClientWrapper(targetDevice.Id);
        if (clientWrapper == null)
        {
            _logger.LogWarning("未找到ID为 {DeviceId} 的线程安全客户端包装器", targetDevice.Id);
            throw new InvalidModbusRequestException($"未找到ID为 {targetDevice.Id} 的线程安全客户端包装器",
                (byte)ModbusExceptionCode.GatewayPathUnavailable);
        }

        // 确保连接状态
        await EnsureClientConnectedAsync(targetDevice);

        // 修改请求报文中的从机地址、起始地址和寄存器数量
        var newRequest = ModifyRequest(originalRequest, modbusRequest.SlaveId, modbusRequest.StartAddress, modbusRequest.Quantity);

        // 使用线程安全的客户端包装器处理请求
        return await ProcessRequestWithClientWrapperAsync(clientWrapper, newRequest, originalSlaveId, originalRequest[7]);
    }

    /// <summary>
    /// 确保客户端已连接
    /// </summary>
    /// <param name="targetDevice">目标设备</param>
    private async Task EnsureClientConnectedAsync(TargetDevice targetDevice)
    {
        var rawClient = _clientPool.GetRawClient(targetDevice.Id);
        if (rawClient == null)
        {
            throw new InvalidModbusRequestException($"未找到设备 {targetDevice.Id} 的原始客户端",
                (byte)ModbusExceptionCode.GatewayPathUnavailable);
        }

        if (targetDevice.PortType == PortType.Tcp)
        {
            if (targetDevice is TcpTargetDevice tcpTargetDevice && rawClient is ModbusTcpClient targetTcpClient)
            {
                if (!targetTcpClient.IsConnected)
                {
                    _logger.LogInformation("重新连接到设备 {DeviceId} ({IpAddress}:{Port})",
                        tcpTargetDevice.Id, tcpTargetDevice.Ip, tcpTargetDevice.Port);

                    try
                    {
                        await Task.Run(() => targetTcpClient.Connect(new IPEndPoint(IPAddress.Parse(tcpTargetDevice.Ip), tcpTargetDevice.Port)));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "重新连接到设备 {DeviceId} 失败", tcpTargetDevice.Id);
                        throw new InvalidModbusRequestException($"重新连接到设备 {tcpTargetDevice.Id} 失败",
                            (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
                    }
                }
            }
        }
        else if (targetDevice.PortType == PortType.Rtu)
        {
            if (targetDevice is RtuTargetDevice rtuTargetDevice && rawClient is ModbusRtuClient targetRtuClient)
            {
                if (!targetRtuClient.IsConnected)
                {
                    _logger.LogInformation("重新连接到设备 {DeviceId}, PortName = {Port})", rtuTargetDevice.Id, rtuTargetDevice.PortName);

                    try
                    {
                        await Task.Run(() => targetRtuClient.Connect(new SerialPort(rtuTargetDevice.PortName, rtuTargetDevice.BaudRate,
                            rtuTargetDevice.Parity, rtuTargetDevice.DataBits, rtuTargetDevice.StopBits)));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "重新连接到设备 {DeviceId} 失败", rtuTargetDevice.Id);
                        throw new InvalidModbusRequestException($"重新连接到设备 {rtuTargetDevice.Id} 失败",
                            (byte)ModbusExceptionCode.GatewayTargetDeviceFailedToRespond);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 使用线程安全的客户端包装器处理请求
    /// </summary>
    /// <param name="clientWrapper">线程安全的客户端包装器</param>
    /// <param name="request">请求数据</param>
    /// <param name="originalSlaveId">原始从站ID</param>
    /// <param name="functionCode">功能码</param>
    /// <returns>响应数据</returns>
    private async Task<byte[]> ProcessRequestWithClientWrapperAsync(ModbusClientWrapper clientWrapper, byte[] request, byte originalSlaveId, byte functionCode)
    {
        // 解析请求参数
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        // 根据功能码调用相应的线程安全方法
        switch (functionCode)
        {
            case 1: // 读取线圈
                var coils = await clientWrapper.ReadCoilsAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildCoilsResponse(request, originalSlaveId, coils, quantity);

            case 2: // 读取离散输入
                var inputs = await clientWrapper.ReadDiscreteInputsAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildDiscreteInputsResponse(request, originalSlaveId, inputs, quantity);

            case 3: // 读取保持寄存器
                var holdingRegisters = await clientWrapper.ReadHoldingRegistersAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildHoldingRegistersResponse(request, originalSlaveId, holdingRegisters, quantity);

            case 4: // 读取输入寄存器
                var inputRegisters = await clientWrapper.ReadInputRegistersAsync(slaveId, startAddress, quantity, _stoppingCts.Token);
                return BuildInputRegistersResponse(request, originalSlaveId, inputRegisters, quantity);

            case 5: // 写入单个线圈
                var coilValue = (request[10] == 0xFF);
                await clientWrapper.WriteSingleCoilAsync(slaveId, startAddress, coilValue, _stoppingCts.Token);
                return BuildWriteSingleCoilResponse(request, originalSlaveId);

            case 6: // 写入单个寄存器
                var registerValue = (ushort)((request[10] << 8) | request[11]);
                await clientWrapper.WriteSingleRegisterAsync(slaveId, startAddress, registerValue, _stoppingCts.Token);
                return BuildWriteSingleRegisterResponse(request, originalSlaveId);

            case 15: // 写入多个线圈
                var coilValues = ExtractCoilValues(request, quantity);
                await clientWrapper.WriteMultipleCoilsAsync(slaveId, startAddress, coilValues, _stoppingCts.Token);
                return BuildWriteMultipleCoilsResponse(request, originalSlaveId, startAddress, quantity);

            case 16: // 写入多个寄存器
                var registerValues = ExtractRegisterValues(request, quantity);
                await clientWrapper.WriteMultipleRegistersAsync(slaveId, startAddress, registerValues, _stoppingCts.Token);
                return BuildWriteMultipleRegistersResponse(request, originalSlaveId, startAddress, quantity);

            default:
                throw new InvalidModbusRequestException($"不支持的功能码: {functionCode}",
                    (byte)ModbusExceptionCode.InvalidFunctionCode);
        }
    }

    // 响应构建方法
    private static byte[] BuildCoilsResponse(byte[] request, byte originalSlaveId, Memory<byte> coils, ushort quantity)
    {
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];

        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originalSlaveId;
        response[7] = 1;
        response[8] = (byte)dataLength;

        coils.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildDiscreteInputsResponse(byte[] request, byte originalSlaveId, Memory<byte> inputs, ushort quantity)
    {
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];

        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originalSlaveId;
        response[7] = 2;
        response[8] = (byte)dataLength;

        inputs.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildHoldingRegistersResponse(byte[] request, byte originalSlaveId, Memory<byte> registers, ushort quantity)
    {
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];

        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originalSlaveId;
        response[7] = 3;
        response[8] = (byte)dataLength;

        registers.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildInputRegistersResponse(byte[] request, byte originalSlaveId, Memory<byte> registers, ushort quantity)
    {
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];

        Array.Copy(request, 0, response, 0, 6);
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);
        response[6] = originalSlaveId;
        response[7] = 4;
        response[8] = (byte)dataLength;

        registers.Span.CopyTo(response.AsSpan(9));
        return response;
    }

    private static byte[] BuildWriteSingleCoilResponse(byte[] request, byte originalSlaveId)
    {
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originalSlaveId;
        return response;
    }

    private static byte[] BuildWriteSingleRegisterResponse(byte[] request, byte originalSlaveId)
    {
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originalSlaveId;
        return response;
    }

    private static byte[] BuildWriteMultipleCoilsResponse(byte[] request, byte originalSlaveId, ushort startAddress, ushort quantity)
    {
        var response = new byte[12];
        Array.Copy(request, 0, response, 0, 6);
        response[4] = 0;
        response[5] = 6;
        response[6] = originalSlaveId;
        response[7] = 15;
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;
        return response;
    }

    private static byte[] BuildWriteMultipleRegistersResponse(byte[] request, byte originalSlaveId, ushort startAddress, ushort quantity)
    {
        var response = new byte[12];
        Array.Copy(request, 0, response, 0, 6);
        response[4] = 0;
        response[5] = 6;
        response[6] = originalSlaveId;
        response[7] = 16;
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;
        return response;
    }

    // 数据提取方法
    private static bool[] ExtractCoilValues(byte[] request, ushort quantity)
    {
        var values = new bool[quantity];
        for (var i = 0; i < quantity; i++)
        {
            var byteIndex = 13 + (i / 8);
            var bitIndex = i % 8;
            values[i] = ((request[byteIndex] >> bitIndex) & 1) == 1;
        }
        return values;
    }

    private static ushort[] ExtractRegisterValues(byte[] request, ushort quantity)
    {
        var values = new ushort[quantity];
        for (var i = 0; i < quantity; i++)
        {
            values[i] = (ushort)((request[13 + i * 2] << 8) | request[14 + i * 2]);
        }
        return values;
    }
}