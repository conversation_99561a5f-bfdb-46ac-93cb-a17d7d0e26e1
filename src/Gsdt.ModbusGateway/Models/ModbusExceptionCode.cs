using System.ComponentModel;

namespace Gsdt.ModbusGateway.Models;

public enum ModbusExceptionCode : byte
{ 
    /// <summary>
    /// 非法功能码
    /// </summary>
    [Description("非法功能码")]
    InvalidFunctionCode = 1,
    
    /// <summary>
    /// 非法数据地址
    /// </summary>
    [Description("非法数据地址")]
    InvalidAddress = 2,
    
    /// <summary>
    /// 非法数据值
    /// </summary>
    [Description("非法数据值")]
    InvalidValue = 3,
    
    /// <summary>
    /// 从设备故障
    /// </summary>
    [Description("从设备故障")]
    SlaveDeviceFailure = 4,
    
    /// <summary>
    /// 从设备忙
    /// </summary>
    [Description("从设备忙")]
    SlaveDeviceBusy = 6,
    
    /// <summary>
    /// 内存奇偶校验错误
    /// </summary>
    [Description("内存奇偶校验错误")]
    MemoryParityError = 8,
    
    /// <summary>
    /// 网关路径不可用
    /// </summary>
    [Description("网关路径不可用")]
    GatewayPathUnavailable = 10,
    
    /// <summary>
    /// 网关目标设备无法响应
    /// </summary>
    [Description("网关目标设备无法响应")]
    GatewayTargetDeviceFailedToRespond = 11
}