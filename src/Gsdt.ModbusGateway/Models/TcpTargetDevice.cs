using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models;

[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class TcpTargetDevice : TargetDevice
{
    public TcpTargetDevice()
    {
        PortType = PortType.Tcp;
    }

    [JsonPropertyName("Ip")]
    [Required(ErrorMessage = "IP地址是必需的")]
    [RegularExpression(@"^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$", ErrorMessage = "IP地址格式无效")]
    public string Ip { get; set; } = string.Empty;

    [JsonPropertyName("Port")]
    [Required(ErrorMessage = "端口是必需的")]
    [Range(1, 65535, ErrorMessage = "端口必须在1-65535之间")]
    public int Port { get; set; }
}