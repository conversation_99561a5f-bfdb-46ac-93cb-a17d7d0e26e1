using System.Net;
using System.Net.Sockets;
using NModbus;

namespace Gsdt.ModbusGateway.Models;

/// <summary>
/// Modbus TCP 客户端
/// </summary>
public class ModbusTcpClient : ModbusClient
{
    private TcpClient _tcpClient = new();
    private readonly ModbusFactory _factory = new();
    
    /// <summary>
    /// 获取客户端是否已连接
    /// </summary>
    public bool IsConnected => _tcpClient.Connected;
    
    /// <summary>
    /// 连接到Modbus服务器
    /// </summary>
    /// <param name="endPoint">服务器端点</param>
    public void Connect(IPEndPoint endPoint)
    {
        if (_tcpClient.Connected)
        {
            Disconnect();
        }

        _tcpClient = new TcpClient();
        _tcpClient.Connect(endPoint);
        _tcpClient.ReceiveTimeout = (int)Timeout.TotalMilliseconds;
        _tcpClient.SendTimeout = (int)Timeout.TotalMilliseconds;
        Master = _factory.CreateMaster(_tcpClient);
    }
    
    /// <summary>
    /// 断开连接
    /// </summary>
    public void Disconnect()
    {
        if (_tcpClient.Connected)
        {
            _tcpClient.Close();
        }
    }
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        Disconnect();
        _tcpClient.Dispose();
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}