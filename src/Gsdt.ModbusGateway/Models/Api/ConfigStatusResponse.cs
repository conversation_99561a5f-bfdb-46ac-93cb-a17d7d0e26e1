using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models.Api;

/// <summary>
/// 配置状态响应
/// </summary>
[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class ConfigStatusResponse
{
    /// <summary>
    /// 当前配置文件
    /// </summary>
    [JsonPropertyName("currentConfigFile")]
    public string CurrentConfigFile { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用配置重载
    /// </summary>
    [JsonPropertyName("enableConfigReload")]
    public bool EnableConfigReload { get; set; }

    /// <summary>
    /// 配置重载间隔
    /// </summary>
    [JsonPropertyName("configReloadInterval")]
    public int ConfigReloadInterval { get; set; }

    /// <summary>
    /// 设备数量信息
    /// </summary>
    [JsonPropertyName("deviceCount")]
    public DeviceCountInfo DeviceCount { get; set; } = new();

    /// <summary>
    /// 路由数量
    /// </summary>
    [JsonPropertyName("routeCount")]
    public int RouteCount { get; set; }

    /// <summary>
    /// 监听端口列表
    /// </summary>
    [JsonPropertyName("listenPorts")]
    public List<int> ListenPorts { get; set; } = [];
}

/// <summary>
/// 设备数量信息
/// </summary>
[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class DeviceCountInfo
{
    /// <summary>
    /// TCP设备数量
    /// </summary>
    [JsonPropertyName("tcpDevices")]
    public int TcpDevices { get; set; }

    /// <summary>
    /// RTU设备数量
    /// </summary>
    [JsonPropertyName("rtuDevices")]
    public int RtuDevices { get; set; }

    /// <summary>
    /// 总设备数量
    /// </summary>
    [JsonPropertyName("totalDevices")]
    public int TotalDevices { get; set; }
}

/// <summary>
/// 配置文件信息响应
/// </summary>
[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class ConfigFileResponse
{
    /// <summary>
    /// 文件名
    /// </summary>
    [JsonPropertyName("fileName")]
    public string FileName { get; set; } = string.Empty;

    /// <summary>
    /// 文件内容
    /// </summary>
    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// 通用消息响应
/// </summary>
[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class MessageResponse
{
    /// <summary>
    /// 消息内容
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// 错误响应
/// </summary>
[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class ErrorResponse
{
    /// <summary>
    /// 错误信息
    /// </summary>
    [JsonPropertyName("error")]
    public string Error { get; set; } = string.Empty;

    /// <summary>
    /// 详细信息
    /// </summary>
    [JsonPropertyName("details")]
    public string? Details { get; set; }
}
