using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models;

/// <summary>
/// 应用程序配置类，用于管理网关配置文件路径
/// </summary>
[DynamicallyAccessedMembers(DynamicallyAccessedMemberTypes.PublicProperties)]
public class AppConfig
{
    /// <summary>
    /// 网关配置文件路径
    /// 如果为空或null，则使用appsettings.json中的GatewayConfig节点（向后兼容）
    /// </summary>
    [JsonPropertyName("GatewayConfigFile")]
    public string? GatewayConfigFile { get; set; }

    /// <summary>
    /// 是否启用配置文件热重载
    /// </summary>
    [JsonPropertyName("EnableConfigReload")]
    public bool EnableConfigReload { get; set; } = true;

    /// <summary>
    /// 配置文件监控间隔（毫秒）
    /// </summary>
    [JsonPropertyName("ConfigReloadInterval")]
    [Range(1000, 60000, ErrorMessage = "配置文件监控间隔必须在1000-60000毫秒之间")]
    public int ConfigReloadInterval { get; set; } = 5000;
}
