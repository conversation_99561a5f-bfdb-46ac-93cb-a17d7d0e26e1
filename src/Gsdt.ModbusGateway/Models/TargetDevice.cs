using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Gsdt.ModbusGateway.Models;

public class TargetDevice
{
    [Required(ErrorMessage = "设备ID是必需的")]
    [JsonPropertyName("Id")]
    public int Id { get; set; }
    
    [Required(ErrorMessage = "端口类型是必需的")]
    [JsonPropertyName("PortType")]
    public PortType PortType { get; set; }

    [Required(ErrorMessage = "超时时间是必需的")]
    [Range(100, 30000, ErrorMessage = "超时时间必须在100-30000毫秒之间")]
    [JsonPropertyName("Timeout")]
    public int Timeout { get; set; } = 1000;
}