using System.IO.Ports;
using NModbus;
using NModbus.IO;
using NModbus.Serial;

namespace Gsdt.ModbusGateway.Models;

public class ModbusRtuClient : ModbusClient
{
    private SerialPort _serialPort = new();
    private readonly ModbusFactory _factory = new();
    
    /// <summary>
    /// 获取客户端是否已连接
    /// </summary>
    public bool IsConnected => _serialPort.IsOpen;
    
    public void Connect(SerialPort port)
    {
        _serialPort = port;
        
        if (_serialPort.IsOpen)
        {
            Disconnect();
        }
        
        _serialPort.ReadTimeout = (int)Timeout.TotalMilliseconds;
        _serialPort.WriteTimeout = (int)Timeout.TotalMilliseconds;
        _serialPort.Open();
        
        Master = _factory.CreateRtuMaster(new SerialPortAdapter(port));
    }
    
    /// <summary>
    /// 断开连接
    /// </summary>
    public void Disconnect()
    {
        if (_serialPort.IsOpen)
        {
            _serialPort.Close();
        }
    }
    
    /// <summary>
    /// 释放资源
    /// </summary>
    public override void Dispose()
    {
        Disconnect();
        _serialPort.Dispose();
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}