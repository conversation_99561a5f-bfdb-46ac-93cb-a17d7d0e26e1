namespace Gsdt.ModbusGateway.Models;

/// <summary>
/// Modbus请求
/// </summary>
public class ModbusRequest
{
    public int DeviceId { get; set; }
    public ushort SlaveId { get; set; }
    public byte FunctionCode { get; set; }
    public ushort StartAddress { get; set; }
    public ushort Quantity { get; set; }

    public ushort SourceStartAddress { get; set; }
}

public class ModbusResponse
{
    public byte[] Data { get; set; } = [];
    public ushort SourceStartAddress { get; set; }
}