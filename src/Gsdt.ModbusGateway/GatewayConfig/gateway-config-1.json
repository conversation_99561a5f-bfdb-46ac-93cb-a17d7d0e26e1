{"TcpTargetDevices": [{"Id": 1001, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1002, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1003, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1004, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1005, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1006, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1007, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1008, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1009, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1010, "Ip": "***************", "Port": 8080, "Timeout": 1000}], "RtuTargetDevices": [{"Id": 2001, "PortName": "COM33", "BaudRate": 9600, "Parity": 0, "DataBits": 8, "StopBits": 1, "Timeout": 1000}, {"Id": 2002, "PortName": "COM34", "BaudRate": 9600, "Parity": 0, "DataBits": 8, "StopBits": 1, "Timeout": 1000}], "Routes": [{"Port": 8080, "DefaultDeviceId": 1001, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8081, "DefaultDeviceId": 1002, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8082, "DefaultDeviceId": 1003, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8083, "DefaultDeviceId": 1004, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8084, "DefaultDeviceId": 1005, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8085, "DefaultDeviceId": 1006, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8086, "DefaultDeviceId": 1007, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8087, "DefaultDeviceId": 1008, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8088, "DefaultDeviceId": 1009, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}, {"Port": 8089, "DefaultDeviceId": 1010, "Transforms": [{"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 25, "TargetStartAddress": 1, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 26, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2002, "TargetSlaveId": 2, "SourceStartAddress": 27, "TargetStartAddress": 2, "Quantity": 1, "FunctionCodes": [3]}, {"TargetDeviceId": 2001, "TargetSlaveId": 254, "SourceStartAddress": 37, "TargetStartAddress": 0, "Quantity": 1, "FunctionCodes": [2]}]}]}