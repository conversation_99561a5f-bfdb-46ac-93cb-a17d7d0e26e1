# 网关配置文件说明

## 概述

此文件夹包含Modbus网关的配置文件。新的配置架构支持：

- 动态配置文件切换
- 配置热重载
- 向后兼容性
- 配置文件管理API

## 配置文件结构

### 主配置文件 (appsettings.json)

```json
{
  "AppConfig": {
    "GatewayConfigFile": "GatewayConfig/gateway-config-1.json",
    "EnableConfigReload": true,
    "ConfigReloadInterval": 5000
  }
}
```

### 网关配置文件

网关配置文件包含以下部分：

1. **TcpTargetDevices**: TCP目标设备列表
2. **RtuTargetDevices**: RTU目标设备列表  
3. **Routes**: 路由配置列表

## 配置文件示例

### gateway-config-1.json
完整的生产环境配置，包含10个TCP设备和2个RTU设备，以及对应的路由配置。

### gateway-config-2.json
简化的测试环境配置，包含2个TCP设备和1个RTU设备。

## 配置切换

### 方法1：修改appsettings.json

修改 `AppConfig.GatewayConfigFile` 的值：

```json
{
  "AppConfig": {
    "GatewayConfigFile": "GatewayConfig/gateway-config-2.json"
  }
}
```

### 方法2：使用API

```bash
# 获取当前配置状态
GET /api/config/status

# 列出可用配置文件
GET /api/config/files

# 获取特定配置文件内容
GET /api/config/files/gateway-config-1.json

# 重新加载配置
POST /api/config/reload
```

## 向后兼容性

如果 `GatewayConfigFile` 为空或未设置，系统将自动从 `appsettings.json` 的 `GatewayConfig` 节点加载配置，保持与旧版本的兼容性。

## 配置热重载

当启用配置热重载时（`EnableConfigReload: true`），系统会监控配置文件的变化并自动重新加载。

**注意**: 配置热重载可能需要重启相关服务才能完全生效。

## 配置验证

所有配置文件在加载时都会进行验证，确保：

- 必需字段存在
- 数据类型正确
- 值在有效范围内
- 设备ID唯一性

## 最佳实践

1. **备份配置**: 在修改配置前备份原始文件
2. **测试配置**: 在生产环境使用前在测试环境验证配置
3. **监控日志**: 配置变更后检查应用程序日志
4. **版本控制**: 将配置文件纳入版本控制系统

## 故障排除

### 配置文件不存在
- 检查文件路径是否正确
- 确保文件具有正确的读取权限

### 配置验证失败
- 检查JSON格式是否正确
- 验证所有必需字段是否存在
- 确保数据类型和值范围正确

### 热重载不工作
- 检查 `EnableConfigReload` 是否为 `true`
- 确保应用程序有文件系统监控权限
- 查看应用程序日志获取详细错误信息
