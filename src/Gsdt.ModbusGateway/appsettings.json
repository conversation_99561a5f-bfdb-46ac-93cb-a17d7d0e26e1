{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"restrictedToMinimumLevel": "Information"}}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 15, "restrictedToMinimumLevel": "Debug", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppConfig": {"GatewayConfigFile": "GatewayConfig/gateway-config-2.json", "EnableConfigReload": true, "ConfigReloadInterval": 5000}}