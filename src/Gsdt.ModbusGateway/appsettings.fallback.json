{"Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Information", "System": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"restrictedToMinimumLevel": "Information"}}, {"Name": "File", "Args": {"path": "logs/log-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 15, "restrictedToMinimumLevel": "Debug", "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppConfig": {"GatewayConfigFile": "", "EnableConfigReload": false, "ConfigReloadInterval": 5000}, "GatewayConfig": {"TcpTargetDevices": [{"Id": 1001, "Ip": "***************", "Port": 8080, "Timeout": 1000}, {"Id": 1002, "Ip": "***************", "Port": 8080, "Timeout": 1000}], "RtuTargetDevices": [{"Id": 2001, "PortName": "COM33", "BaudRate": 9600, "Parity": 0, "DataBits": 8, "StopBits": 1, "Timeout": 1000}], "Routes": [{"Port": 8080, "DefaultDeviceId": 1001, "Transforms": [{"TargetDeviceId": 2001, "TargetSlaveId": 1, "SourceStartAddress": 0, "TargetStartAddress": 0, "Quantity": 10, "FunctionCodes": [3, 4]}]}]}}