using System.Collections.Concurrent;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// 线程安全的ModbusClient连接池管理器
/// 为每个设备维护一个请求队列，确保串行处理
/// </summary>
public class ThreadSafeModbusClientPool : IDisposable
{
    private readonly ILogger<ThreadSafeModbusClientPool> _logger;
    private readonly ConcurrentDictionary<int, ModbusClientWrapper> _clientWrappers = new();
    private readonly ConcurrentDictionary<int, ModbusClient> _clients = new();
    private readonly ModbusPerformanceMonitor? _performanceMonitor;
    private bool _disposed = false;

    public ThreadSafeModbusClientPool(ILogger<ThreadSafeModbusClientPool> logger, ModbusPerformanceMonitor? performanceMonitor = null)
    {
        _logger = logger;
        _performanceMonitor = performanceMonitor;
    }

    /// <summary>
    /// 注册ModbusClient到连接池
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="client">ModbusClient实例</param>
    public void RegisterClient(int deviceId, ModbusClient client)
    {
        _clients[deviceId] = client;
        _clientWrappers[deviceId] = new ModbusClientWrapper(client, _logger, deviceId, _performanceMonitor);
        _logger.LogDebug("已注册设备 {DeviceId} 的线程安全客户端包装器", deviceId);
    }

    /// <summary>
    /// 获取线程安全的ModbusClient包装器
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>线程安全的ModbusClient包装器</returns>
    public ModbusClientWrapper? GetClientWrapper(int deviceId)
    {
        return _clientWrappers.TryGetValue(deviceId, out var wrapper) ? wrapper : null;
    }

    /// <summary>
    /// 获取原始ModbusClient（用于连接管理）
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>原始ModbusClient</returns>
    public ModbusClient? GetRawClient(int deviceId)
    {
        return _clients.TryGetValue(deviceId, out var client) ? client : null;
    }

    /// <summary>
    /// 移除设备的客户端
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    public void RemoveClient(int deviceId)
    {
        if (_clientWrappers.TryRemove(deviceId, out var wrapper))
        {
            wrapper.Dispose();
            _logger.LogDebug("已移除设备 {DeviceId} 的线程安全客户端包装器", deviceId);
        }
        _clients.TryRemove(deviceId, out _);
    }

    /// <summary>
    /// 获取所有已注册的设备ID
    /// </summary>
    /// <returns>设备ID列表</returns>
    public IEnumerable<int> GetRegisteredDeviceIds()
    {
        return _clients.Keys;
    }

    /// <summary>
    /// 检查设备是否已注册
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>是否已注册</returns>
    public bool IsDeviceRegistered(int deviceId)
    {
        return _clients.ContainsKey(deviceId);
    }

    /// <summary>
    /// 清理所有客户端连接（用于配置热重载）
    /// </summary>
    public void ClearAllClients()
    {
        _logger.LogInformation("开始清理所有客户端连接...");

        // 先释放所有包装器
        foreach (var wrapper in _clientWrappers.Values)
        {
            wrapper.Dispose();
        }

        // 断开并释放所有原始客户端
        foreach (var (deviceId, client) in _clients)
        {
            try
            {
                if (client is ModbusTcpClient { IsConnected: true } tcpClient)
                {
                    tcpClient.Disconnect();
                    tcpClient.Dispose();
                }
                else if (client is ModbusRtuClient { IsConnected: true } rtuClient)
                {
                    rtuClient.Disconnect();
                    rtuClient.Dispose();
                }
                else
                {
                    client.Dispose();
                }

                _logger.LogDebug("已断开设备 {DeviceId} 的连接", deviceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "断开设备 {DeviceId} 连接时发生错误", deviceId);
            }
        }

        // 清空集合
        _clientWrappers.Clear();
        _clients.Clear();

        _logger.LogInformation("所有客户端连接已清理完成");
    }

    public void Dispose()
    {
        if (_disposed) return;

        _logger.LogDebug("正在释放线程安全ModbusClient连接池资源");

        foreach (var wrapper in _clientWrappers.Values)
        {
            wrapper.Dispose();
        }

        _clientWrappers.Clear();
        _clients.Clear();
        _disposed = true;

        _logger.LogDebug("线程安全ModbusClient连接池资源已释放");
    }
}

/// <summary>
/// 线程安全的ModbusClient包装器
/// 使用SemaphoreSlim确保对同一设备的请求串行处理
/// </summary>
public class ModbusClientWrapper : IDisposable
{
    private readonly ModbusClient _client;
    private readonly SemaphoreSlim _semaphore;
    private readonly ILogger _logger;
    private readonly ModbusPerformanceMonitor? _performanceMonitor;
    private readonly int _deviceId;
    private bool _disposed = false;

    public ModbusClientWrapper(ModbusClient client, ILogger logger, int deviceId, ModbusPerformanceMonitor? performanceMonitor = null)
    {
        _client = client;
        _logger = logger;
        _deviceId = deviceId;
        _performanceMonitor = performanceMonitor;
        // 使用信号量确保同一时间只有一个请求在处理
        // 这对于Modbus-RTU协议的串行处理要求特别重要
        _semaphore = new SemaphoreSlim(1, 1);
    }

    /// <summary>
    /// 线程安全的读取线圈
    /// </summary>
    public async Task<Memory<byte>> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusClientWrapper));

        var requestId = _performanceMonitor?.StartRequest(_deviceId, 1); // 功能码1：读取线圈

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ModbusClientWrapper));

            _performanceMonitor?.MarkRequestStartExecution(requestId ?? "");

            _logger.LogDebug("开始读取线圈: DeviceId={DeviceId}, SlaveId={SlaveId}, StartAddress={StartAddress}, Quantity={Quantity}",
                _deviceId, slaveId, startAddress, quantity);

            var result = await _client.ReadCoilsAsync(slaveId, startAddress, quantity, cancellationToken);

            _performanceMonitor?.CompleteRequest(requestId ?? "", true);
            return result;
        }
        catch (Exception ex)
        {
            _performanceMonitor?.CompleteRequest(requestId ?? "", false, ex.Message);
            throw;
        }
        finally
        {
            try
            {
                _semaphore.Release();
            }
            catch (ObjectDisposedException)
            {
                // 信号量已被释放，忽略异常
            }
        }
    }

    /// <summary>
    /// 线程安全的读取离散输入
    /// </summary>
    public async Task<Memory<byte>> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        return await ExecuteWithMonitoringAsync(2, async () =>
        {
            _logger.LogDebug("开始读取离散输入: DeviceId={DeviceId}, SlaveId={SlaveId}, StartAddress={StartAddress}, Quantity={Quantity}",
                _deviceId, slaveId, startAddress, quantity);
            return await _client.ReadDiscreteInputsAsync(slaveId, startAddress, quantity, cancellationToken);
        }, cancellationToken);
    }

    /// <summary>
    /// 线程安全的读取保持寄存器
    /// </summary>
    public async Task<Memory<byte>> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusClientWrapper));

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ModbusClientWrapper));

            _logger.LogDebug("开始读取保持寄存器: SlaveId={SlaveId}, StartAddress={StartAddress}, Quantity={Quantity}",
                slaveId, startAddress, quantity);
            return await _client.ReadHoldingRegistersAsync(slaveId, startAddress, quantity, cancellationToken);
        }
        finally
        {
            try
            {
                _semaphore.Release();
            }
            catch (ObjectDisposedException)
            {
                // 信号量已被释放，忽略异常
            }
        }
    }

    /// <summary>
    /// 线程安全的读取输入寄存器
    /// </summary>
    public async Task<Memory<byte>> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("开始读取输入寄存器: SlaveId={SlaveId}, StartAddress={StartAddress}, Quantity={Quantity}",
                slaveId, startAddress, quantity);
            return await _client.ReadInputRegistersAsync(slaveId, startAddress, quantity, cancellationToken);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 线程安全的写入单个线圈
    /// </summary>
    public async Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusClientWrapper));

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ModbusClientWrapper));

            _logger.LogDebug("开始写入单个线圈: SlaveId={SlaveId}, Address={Address}, Value={Value}",
                slaveId, address, value);
            await _client.WriteSingleCoilAsync(slaveId, address, value, cancellationToken);
        }
        finally
        {
            try
            {
                _semaphore.Release();
            }
            catch (ObjectDisposedException)
            {
                // 信号量已被释放，忽略异常
            }
        }
    }

    /// <summary>
    /// 线程安全的写入单个寄存器
    /// </summary>
    public async Task WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value, CancellationToken cancellationToken)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusClientWrapper));

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ModbusClientWrapper));

            _logger.LogDebug("开始写入单个寄存器: SlaveId={SlaveId}, Address={Address}, Value={Value}",
                slaveId, address, value);
            await _client.WriteSingleRegisterAsync(slaveId, address, value, cancellationToken);
        }
        finally
        {
            try
            {
                _semaphore.Release();
            }
            catch (ObjectDisposedException)
            {
                // 信号量已被释放，忽略异常
            }
        }
    }

    /// <summary>
    /// 线程安全的写入多个线圈
    /// </summary>
    public async Task WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values, CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("开始写入多个线圈: SlaveId={SlaveId}, StartAddress={StartAddress}, Count={Count}",
                slaveId, startAddress, values.Length);
            await _client.WriteMultipleCoilsAsync(slaveId, startAddress, values, cancellationToken);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 线程安全的写入多个寄存器
    /// </summary>
    public async Task WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values, CancellationToken cancellationToken)
    {
        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogDebug("开始写入多个寄存器: SlaveId={SlaveId}, StartAddress={StartAddress}, Count={Count}",
                slaveId, startAddress, values.Length);
            await _client.WriteMultipleRegistersAsync(slaveId, startAddress, values, cancellationToken);
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 执行带性能监控的操作
    /// </summary>
    private async Task<T> ExecuteWithMonitoringAsync<T>(byte functionCode, Func<Task<T>> operation, CancellationToken cancellationToken)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ModbusClientWrapper));

        var requestId = _performanceMonitor?.StartRequest(_deviceId, functionCode);

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(ModbusClientWrapper));

            _performanceMonitor?.MarkRequestStartExecution(requestId ?? "");
            var result = await operation();
            _performanceMonitor?.CompleteRequest(requestId ?? "", true);
            return result;
        }
        catch (Exception ex)
        {
            _performanceMonitor?.CompleteRequest(requestId ?? "", false, ex.Message);
            throw;
        }
        finally
        {
            try
            {
                _semaphore.Release();
            }
            catch (ObjectDisposedException)
            {
                // 信号量已被释放，忽略异常
            }
        }
    }

    /// <summary>
    /// 执行带性能监控的操作（无返回值）
    /// </summary>
    private async Task ExecuteWithMonitoringAsync(byte functionCode, Func<Task> operation, CancellationToken cancellationToken)
    {
        var requestId = _performanceMonitor?.StartRequest(_deviceId, functionCode);

        await _semaphore.WaitAsync(cancellationToken);
        try
        {
            _performanceMonitor?.MarkRequestStartExecution(requestId ?? "");
            await operation();
            _performanceMonitor?.CompleteRequest(requestId ?? "", true);
        }
        catch (Exception ex)
        {
            _performanceMonitor?.CompleteRequest(requestId ?? "", false, ex.Message);
            throw;
        }
        finally
        {
            _semaphore.Release();
        }
    }

    /// <summary>
    /// 获取原始客户端（用于连接管理）
    /// </summary>
    public ModbusClient GetRawClient() => _client;

    public void Dispose()
    {
        if (_disposed) return;

        _semaphore?.Dispose();
        _disposed = true;
    }
}
