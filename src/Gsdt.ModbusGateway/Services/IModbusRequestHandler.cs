using Gsdt.ModbusGateway.Models;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus请求处理器接口
/// </summary>
public interface IModbusRequestHandler
{
    /// <summary>
    /// 处理Modbus请求
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="request">请求数据</param>
    /// <param name="originSlaveId">原始从站ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    Task<byte[]> ForwardRequestAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken);

    /// <summary>
    /// 转换请求
    /// </summary>
    /// <param name="request">原始请求</param>
    /// <param name="routes">路由规则</param>
    /// <returns>转换后的请求列表</returns>
    Task<List<ModbusRequest>> TransformRequestAsync(ModbusRequest request, List<RouteConfig> routes);

    /// <summary>
    /// 合并响应
    /// </summary>
    /// <param name="request">原始请求数据</param>
    /// <param name="responseList">响应列表</param>
    /// <returns>合并后的响应数据</returns>
    Task<byte[]> MergeResponsesAsync(byte[] request, List<ModbusResponse> responseList);
}
