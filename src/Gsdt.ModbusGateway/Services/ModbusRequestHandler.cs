using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Models.ModbusResponseMerger;
using Microsoft.Extensions.Logging;
using NModbus;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus请求处理器实现
/// </summary>
public class ModbusRequestHandler : IModbusRequestHandler
{
    private readonly ILogger<ModbusRequestHandler> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ModbusRequestHandler(ILogger<ModbusRequestHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 转发请求到目标设备
    /// </summary>
    /// <param name="client">Modbus客户端</param>
    /// <param name="request">请求数据</param>
    /// <param name="originSlaveId"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应数据</returns>
    public async Task<byte[]> ForwardRequestAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 获取从站ID和功能码
        var slaveId = request[6];
        var functionCode = request[7];

        // 根据功能码处理请求
        switch (functionCode)
        {
            case 1: // 读取线圈
                return await HandleReadCoilsAsync(client, request, originSlaveId, cancellationToken);
            case 2: // 读取离散输入
                return await HandleReadDiscreteInputsAsync(client, request, originSlaveId, cancellationToken);
            case 3: // 读取保持寄存器
                return await HandleReadHoldingRegistersAsync(client, request, originSlaveId, cancellationToken);
            case 4: // 读取输入寄存器
                return await HandleReadInputRegistersAsync(client, request, originSlaveId, cancellationToken);
            case 5: // 写入单个线圈
                return await HandleWriteSingleCoilAsync(client, request, originSlaveId, cancellationToken);
            case 6: // 写入单个寄存器
                return await HandleWriteSingleRegisterAsync(client, request, originSlaveId, cancellationToken);
            case 15: // 写入多个线圈
                return await HandleWriteMultipleCoilsAsync(client, request, originSlaveId, cancellationToken);
            case 16: // 写入多个寄存器
                return await HandleWriteMultipleRegistersAsync(client, request, originSlaveId, cancellationToken);
            default:
                _logger.LogWarning("不支持的功能码：{FunctionCode}", functionCode);
                return CreateExceptionResponse(request, originSlaveId, 1); // 返回非法功能码异常
        }
    }

    /// <summary>
    /// 转换请求
    /// </summary>
    /// <param name="request">原始请求</param>
    /// <param name="routes">路由规则</param>
    /// <returns>转换后的请求列表</returns>
    public async Task<List<ModbusRequest>> TransformRequestAsync(ModbusRequest request, List<RouteConfig> routes)
    {
        await Task.CompletedTask; // 保持异步接口

        // 查找匹配的路由配置
        var matchingRoute = routes.FirstOrDefault(r =>
            r.Transforms.Any(t => t.FunctionCodes.Contains(request.FunctionCode)));

        if (matchingRoute == null)
        {
            // 如果没有找到匹配的路由，返回原始请求
            _logger.LogWarning("未找到功能码 {FunctionCode} 的路由配置", request.FunctionCode);
            return new List<ModbusRequest> { request };
        }

        // 使用路由配置转换请求
        return TransformRequestWithRoute(request, matchingRoute);
    }

    public async Task<byte[]> MergeResponsesAsync(byte[] request, List<ModbusResponse> responseList)
    {
        // 解析请求
        var slaveId = request[6];
        var functionCode = request[7];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        var merge = ResponseMergerFactory.GetMerger(functionCode);
        var response = merge.Merge(responseList, request);
        return response;
    }

    /// <summary>
    /// 创建异常响应
    /// </summary>
    /// <param name="request">请求数据</param>
    /// <param name="originSlaveId"></param>
    /// <param name="exceptionCode">异常代码</param>
    /// <returns>异常响应</returns>
    public static byte[] CreateExceptionResponse(byte[] request, byte originSlaveId, byte exceptionCode)
    {
        var response = new byte[9];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = 0;
        response[5] = 3;

        // 设置从站ID
        response[6] = originSlaveId;

        // 设置功能码（异常功能码 = 功能码 + 0x80）
        response[7] = (byte)(request[7] | 0x80);

        // 设置异常代码
        response[8] = exceptionCode;

        return response;
    }

    /// <summary>
    /// 处理读取线圈请求
    /// </summary>
    private static async Task<byte[]> HandleReadCoilsAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        // 转发请求到目标设备
        var coils = await client.ReadCoilsAsync(slaveId, startAddress, quantity, cancellationToken);

        // 构造响应
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);

        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 1; // 功能码：读取线圈

        // 设置数据长度
        response[8] = (byte)dataLength;

        // 复制数据
        Array.Copy(coils.ToArray(), 0, response, 9, dataLength);

        return response;
    }

    /// <summary>
    /// 处理读取离散输入请求
    /// </summary>
    private static async Task<byte[]> HandleReadDiscreteInputsAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        // 转发请求到目标设备
        var inputs = await client.ReadDiscreteInputsAsync(slaveId, startAddress, quantity, cancellationToken);

        // 构造响应
        var dataLength = (quantity + 7) / 8;
        var response = new byte[9 + dataLength];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);

        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 2; // 功能码：读取离散输入

        // 设置数据长度
        response[8] = (byte)dataLength;

        // 复制数据
        Array.Copy(inputs.ToArray(), 0, response, 9, dataLength);

        return response;
    }

    /// <summary>
    /// 处理读取保持寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleReadHoldingRegistersAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        // 转发请求到目标设备
        var registers = await client.ReadHoldingRegistersAsync(slaveId, startAddress, quantity, cancellationToken);

        // 构造响应
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);

        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 3; // 功能码：读取保持寄存器

        // 设置数据长度
        response[8] = (byte)dataLength;

        // 复制数据
        Array.Copy(registers.ToArray(), 0, response, 9, dataLength);

        return response;
    }

    /// <summary>
    /// 处理读取输入寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleReadInputRegistersAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);

        // 转发请求到目标设备
        var registers = await client.ReadInputRegistersAsync(slaveId, startAddress, quantity, cancellationToken);

        // 构造响应
        var dataLength = quantity * 2;
        var response = new byte[9 + dataLength];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = (byte)((dataLength + 3) >> 8);
        response[5] = (byte)(dataLength + 3);

        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 4; // 功能码：读取输入寄存器

        // 设置数据长度
        response[8] = (byte)dataLength;

        // 复制数据
        Array.Copy(registers.ToArray(), 0, response, 9, dataLength);

        return response;
    }

    /// <summary>
    /// 处理写入单个线圈请求
    /// </summary>
    private static async Task<byte[]> HandleWriteSingleCoilAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var address = (ushort)((request[8] << 8) | request[9]);
        var value = (request[10] == 0xFF);

        // 转发请求到目标设备
        await client.WriteSingleCoilAsync(slaveId, address, value, cancellationToken);

        // 构造响应（与请求相同）
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originSlaveId;

        return response;
    }

    /// <summary>
    /// 处理写入单个寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleWriteSingleRegisterAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var address = (ushort)((request[8] << 8) | request[9]);
        var value = (ushort)((request[10] << 8) | request[11]);

        // 转发请求到目标设备
        await client.WriteSingleRegisterAsync(slaveId, address, value, cancellationToken);

        // 构造响应（与请求相同）
        var response = new byte[request.Length];
        Array.Copy(request, response, request.Length);
        response[6] = originSlaveId;

        return response;
    }

    /// <summary>
    /// 处理写入多个线圈请求
    /// </summary>
    private static async Task<byte[]> HandleWriteMultipleCoilsAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        var byteCount = request[12];

        // 提取线圈值
        var values = new bool[quantity];
        for (var i = 0; i < quantity; i++)
        {
            var byteIndex = 13 + (i / 8);
            var bitIndex = i % 8;
            values[i] = ((request[byteIndex] >> bitIndex) & 1) == 1;
        }

        // 转发请求到目标设备
        await client.WriteMultipleCoilsAsync(slaveId, startAddress, values, cancellationToken);

        // 构造响应
        var response = new byte[12];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = 0;
        response[5] = 6;

        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 15; // 功能码：写入多个线圈

        // 设置起始地址
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;

        // 设置数量
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;

        return response;
    }

    /// <summary>
    /// 处理写入多个寄存器请求
    /// </summary>
    private static async Task<byte[]> HandleWriteMultipleRegistersAsync(ModbusClient client, byte[] request, byte originSlaveId, CancellationToken cancellationToken)
    {
        // 解析请求
        var slaveId = request[6];
        var startAddress = (ushort)((request[8] << 8) | request[9]);
        var quantity = (ushort)((request[10] << 8) | request[11]);
        var byteCount = request[12];

        // 提取寄存器值
        var values = new ushort[quantity];
        for (var i = 0; i < quantity; i++)
        {
            values[i] = (ushort)((request[13 + i * 2] << 8) | request[14 + i * 2]);
        }

        // 转发请求到目标设备
        await client.WriteMultipleRegistersAsync(slaveId, startAddress, values, cancellationToken);

        // 构造响应
        var response = new byte[12];

        // 复制事务ID、协议ID
        Array.Copy(request, 0, response, 0, 6);

        // 设置响应长度
        response[4] = 0;
        response[5] = 6;

        // 设置从站ID和功能码
        response[6] = originSlaveId;
        response[7] = 16; // 功能码：写入多个寄存器

        // 设置起始地址
        response[8] = (byte)(startAddress >> 8);
        response[9] = (byte)startAddress;

        // 设置数量
        response[10] = (byte)(quantity >> 8);
        response[11] = (byte)quantity;

        return response;
    }

    /// <summary>
    /// 根据路由配置转换请求
    /// </summary>
    /// <param name="request">原始请求</param>
    /// <param name="route">路由配置</param>
    /// <returns>转换后的请求列表</returns>
    private static List<ModbusRequest> TransformRequestWithRoute(ModbusRequest request, RouteConfig route)
    {
        var requests = new List<ModbusRequest>();

        // 如果是写入寄存器，则直接转到目标地址
        if (request.FunctionCode >= 5)
        {
            var transform = route.Transforms.FirstOrDefault(t =>
                t.FunctionCodes.Contains(request.FunctionCode) &&
                t.SourceStartAddress == request.StartAddress);

            requests.Add(transform == null
                ? CreateDefaultRequest(route.DefaultDeviceId, request.SlaveId, request.StartAddress, request.Quantity)
                : CreateTransformRequest(transform, request.StartAddress, request.Quantity));
            return requests;
        }

        // 根据路由配置转换请求
        var originalEnd = (ushort)(request.StartAddress + request.Quantity - 1);
        var currentPosition = request.StartAddress;
        var remaining = request.Quantity;
        var transforms = route.Transforms.OrderBy(t => t.SourceStartAddress).ToList();

        foreach (var transform in transforms)
        {
            if (!transform.FunctionCodes.Contains(request.FunctionCode))
            {
                continue;
            }

            if (remaining <= 0) break;

            var transformStart = transform.SourceStartAddress;
            var transformEnd = (ushort)(transform.SourceStartAddress + transform.Quantity - 1);
            var overlapStart = Math.Max(currentPosition, transformStart);
            var overlapEnd = Math.Min(originalEnd, transformEnd);

            // 处理前置未覆盖部分
            if (currentPosition < overlapStart)
            {
                var defaultQuantity = Math.Min((ushort)(overlapStart - currentPosition), remaining);
                requests.Add(CreateDefaultRequest(
                    route.DefaultDeviceId,
                    request.SlaveId,
                    currentPosition,
                    defaultQuantity));

                currentPosition += defaultQuantity;
                remaining -= defaultQuantity;
            }

            // 处理覆盖部分
            if (overlapStart <= overlapEnd && remaining > 0)
            {
                var transformQuantity = (ushort)(overlapEnd - overlapStart + 1);
                requests.Add(CreateTransformRequest(
                    transform,
                    overlapStart,
                    transformQuantity));

                currentPosition += transformQuantity;
                remaining -= transformQuantity;
            }
        }

        // 处理剩余部分
        if (remaining > 0)
        {
            requests.Add(CreateDefaultRequest(
                route.DefaultDeviceId,
                request.SlaveId,
                currentPosition,
                remaining));
        }

        return requests;
    }

    private static ModbusRequest CreateDefaultRequest(int deviceId, ushort slaveId, ushort start, ushort quantity)
    {
        return new ModbusRequest
        {
            DeviceId = deviceId,
            SlaveId = slaveId,
            StartAddress = start,
            Quantity = quantity,
            SourceStartAddress = start
        };
    }

    private static ModbusRequest CreateTransformRequest(TransformRout transform, ushort overlapStart, ushort quantity)
    {
        return new ModbusRequest
        {
            DeviceId = transform.TargetDeviceId,
            SlaveId = transform.TargetSlaveId,
            StartAddress = (ushort)(transform.TargetStartAddress + (overlapStart - transform.SourceStartAddress)),
            Quantity = quantity,
            SourceStartAddress = transform.SourceStartAddress
        };
    }
}
