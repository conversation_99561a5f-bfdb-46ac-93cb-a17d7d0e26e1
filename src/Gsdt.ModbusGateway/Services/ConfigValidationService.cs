using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Gsdt.ModbusGateway.Models;

namespace Gsdt.ModbusGateway.Services;

public interface IConfigValidationService
{
    void ValidateConfig(GatewayConfig config);
}

public class ConfigValidationService : IConfigValidationService
{
    [UnconditionalSuppressMessage("Trimming", "IL2026:Members annotated with 'RequiresUnreferencedCodeAttribute' require dynamic access otherwise can break functionality when trimming application code", Justification = "<Pending>")]
    public void ValidateConfig(GatewayConfig config)
    {
        var validationContext = new ValidationContext(config);
        var validationResults = new List<ValidationResult>();
        
        if (!Validator.TryValidateObject(config, validationContext, validationResults, true))
        {
            var errors = validationResults
                .Select(r => r.ErrorMessage)
                .Where(m => !string.IsNullOrEmpty(m))
                .ToList();
                
            throw new ValidationException($"配置验证失败: {string.Join(", ", errors)}");
        }

        // 验证目标设备列表中的每个设备
        foreach (var deviceErrors in from device in config.TargetDevices 
                 let deviceContext = new ValidationContext(device) 
                 let deviceResults = new List<ValidationResult>() 
                 where !Validator.TryValidateObject(device, deviceContext, deviceResults, true) 
                 select deviceResults
                     .Select(r => r.ErrorMessage)
                     .Where(m => !string.IsNullOrEmpty(m))
                     .ToList())
        {
            throw new ValidationException($"设备配置验证失败: {string.Join(", ", deviceErrors)}");
        }
        
        // 验证目标设备ID不可重复
        if (config.TargetDevices.GroupBy(d => d.Id).Any(g => g.Count() > 1))
        {
            throw new ValidationException("目标设备ID不可重复");
        }
        
        // 验证路由配置
        foreach (var route in config.Routes)
        {
            route.Transforms.ForEach(t =>
            {
                if (t.SourceStartAddress + t.Quantity > ushort.MaxValue || t.TargetStartAddress + t.Quantity > ushort.MaxValue)
                {
                    throw new ValidationException("源地址和数量之和不能超过65535");
                }
            });
            
            var defaultTargetDevice = config.TargetDevices.FirstOrDefault(d => d.Id == route.DefaultDeviceId);
            if (defaultTargetDevice == null)
            {
                throw new ValidationException($"默认目标设备ID {route.DefaultDeviceId} 不存在");
            }

            var routeTargetDeviceIds = route.Transforms.Select(t => t.TargetDeviceId).ToList();
            var allTargetDeviceIds = config.TargetDevices.Select(d => d.Id).ToList();
            
            var invalidTargetDeviceIds = routeTargetDeviceIds.Except(allTargetDeviceIds).ToList();
            if (invalidTargetDeviceIds.Count != 0)
            {
                throw new ValidationException($"目标设备ID {string.Join(", ", invalidTargetDeviceIds)} 不存在");
            }
        }

        if (config.Routes.GroupBy(r => r.Port).Any(g => g.Count() > 1))
        {
            throw new ValidationException("路由配置中的端口不可重复");
        }
    }
} 