using System.Collections.Concurrent;
using System.IO.Ports;
using System.Net;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus连接健康检查服务
/// 定期检查设备连接状态，自动重连断开的设备
/// </summary>
public class ModbusConnectionHealthService : BackgroundService
{
    private readonly ILogger<ModbusConnectionHealthService> _logger;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly GatewayConfig _config;
    private readonly ModbusPerformanceMonitor _performanceMonitor;
    private readonly ConcurrentDictionary<int, DeviceHealthStatus> _deviceHealthStatus = new();
    private readonly TimeSpan _healthCheckInterval = TimeSpan.FromSeconds(30); // 健康检查间隔
    private readonly TimeSpan _reconnectInterval = TimeSpan.FromSeconds(10); // 重连间隔

    public ModbusConnectionHealthService(
        ILogger<ModbusConnectionHealthService> logger,
        ThreadSafeModbusClientPool clientPool,
        IOptions<GatewayConfig> config,
        ModbusPerformanceMonitor performanceMonitor)
    {
        _logger = logger;
        _clientPool = clientPool;
        _config = config.Value;
        _performanceMonitor = performanceMonitor;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // _logger.LogInformation("Modbus连接健康检查服务已启动");
        //
        // // 初始化设备健康状态
        // foreach (var device in _config.TargetDevices)
        // {
        //     _deviceHealthStatus[device.Id] = new DeviceHealthStatus
        //     {
        //         DeviceId = device.Id,
        //         IsHealthy = false,
        //         LastCheckTime = DateTime.UtcNow,
        //         ConsecutiveFailures = 0
        //     };
        // }
        //
        // while (!stoppingToken.IsCancellationRequested)
        // {
        //     try
        //     {
        //         await PerformHealthChecksAsync(stoppingToken);
        //         await Task.Delay(_healthCheckInterval, stoppingToken);
        //     }
        //     catch (OperationCanceledException)
        //     {
        //         _logger.LogInformation("连接健康检查服务正在停止");
        //         break;
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "执行健康检查时发生错误");
        //         await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        //     }
        // }
    }

    /// <summary>
    /// 执行健康检查
    /// </summary>
    private async Task PerformHealthChecksAsync(CancellationToken cancellationToken)
    {
        var tasks = _config.TargetDevices.Select(device =>
            CheckDeviceHealthAsync(device, cancellationToken)).ToArray();

        await Task.WhenAll(tasks);
    }

    /// <summary>
    /// 检查单个设备的健康状态
    /// </summary>
    private async Task CheckDeviceHealthAsync(TargetDevice device, CancellationToken cancellationToken)
    {
        var deviceStatus = _deviceHealthStatus[device.Id];
        var requestId = _performanceMonitor.StartRequest(device.Id, 0); // 使用功能码0表示健康检查

        try
        {
            var isHealthy = await PerformDeviceHealthCheckAsync(device, cancellationToken);

            deviceStatus.LastCheckTime = DateTime.UtcNow;

            if (isHealthy)
            {
                if (!deviceStatus.IsHealthy)
                {
                    _logger.LogInformation("设备 {DeviceId} 健康检查恢复正常", device.Id);
                }
                deviceStatus.IsHealthy = true;
                deviceStatus.ConsecutiveFailures = 0;
                deviceStatus.LastSuccessTime = DateTime.UtcNow;

                _performanceMonitor.CompleteRequest(requestId, true);
            }
            else
            {
                deviceStatus.IsHealthy = false;
                deviceStatus.ConsecutiveFailures++;
                deviceStatus.LastFailureTime = DateTime.UtcNow;

                _logger.LogWarning("设备 {DeviceId} 健康检查失败，连续失败次数: {ConsecutiveFailures}",
                    device.Id, deviceStatus.ConsecutiveFailures);

                _performanceMonitor.CompleteRequest(requestId, false, "Health check failed");

                // 如果连续失败次数达到阈值，尝试重连
                if (deviceStatus.ConsecutiveFailures >= 3 &&
                    DateTime.UtcNow - deviceStatus.LastReconnectAttempt > _reconnectInterval)
                {
                    await AttemptReconnectAsync(device, cancellationToken);
                    deviceStatus.LastReconnectAttempt = DateTime.UtcNow;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查设备 {DeviceId} 健康状态时发生错误", device.Id);
            deviceStatus.IsHealthy = false;
            deviceStatus.ConsecutiveFailures++;
            deviceStatus.LastFailureTime = DateTime.UtcNow;

            _performanceMonitor.CompleteRequest(requestId, false, ex.Message);
        }
    }

    /// <summary>
    /// 执行设备健康检查
    /// </summary>
    private async Task<bool> PerformDeviceHealthCheckAsync(TargetDevice device, CancellationToken cancellationToken)
    {
        var rawClient = _clientPool.GetRawClient(device.Id);
        if (rawClient == null)
        {
            _logger.LogWarning("未找到设备 {DeviceId} 的客户端", device.Id);
            return false;
        }

        try
        {
            // 检查连接状态
            bool isConnected = device.PortType switch
            {
                PortType.Tcp => rawClient is ModbusTcpClient tcpClient && tcpClient.IsConnected,
                PortType.Rtu => rawClient is ModbusRtuClient rtuClient && rtuClient.IsConnected,
                _ => false
            };

            if (!isConnected)
            {
                return false;
            }

            // 执行简单的读取操作来验证连接
            var clientWrapper = _clientPool.GetClientWrapper(device.Id);
            if (clientWrapper == null)
            {
                return false;
            }

            // 尝试读取一个寄存器来验证连接
            // 使用设备ID作为从站ID，地址0，读取1个寄存器
            var slaveId = (byte)Math.Min(device.Id, 255);
            await clientWrapper.ReadHoldingRegistersAsync(slaveId, 0, 1, cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogDebug("设备 {DeviceId} 健康检查失败: {Error}", device.Id, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 尝试重连设备
    /// </summary>
    private async Task AttemptReconnectAsync(TargetDevice device, CancellationToken cancellationToken)
    {
        _logger.LogInformation("尝试重连设备 {DeviceId}", device.Id);

        try
        {
            var rawClient = _clientPool.GetRawClient(device.Id);
            if (rawClient == null)
            {
                _logger.LogWarning("未找到设备 {DeviceId} 的客户端，无法重连", device.Id);
                return;
            }

            // 先断开现有连接
            try
            {
                if (rawClient is ModbusTcpClient tcpClient)
                {
                    if (tcpClient.IsConnected)
                    {
                        await Task.Run(() => tcpClient.Disconnect(), cancellationToken);
                    }
                }
                else if (rawClient is ModbusRtuClient rtuClient)
                {
                    if (rtuClient.IsConnected)
                    {
                        await Task.Run(() => rtuClient.Disconnect(), cancellationToken);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug("断开设备 {DeviceId} 连接时发生错误: {Error}", device.Id, ex.Message);
            }

            // 等待一小段时间
            await Task.Delay(TimeSpan.FromSeconds(1), cancellationToken);

            // 尝试重新连接
            if (device.PortType == PortType.Tcp && device is TcpTargetDevice tcpDevice)
            {
                if (rawClient is ModbusTcpClient tcpClient)
                {
                    await Task.Run(() =>
                        tcpClient.Connect(new IPEndPoint(IPAddress.Parse(tcpDevice.Ip), tcpDevice.Port)),
                        cancellationToken);
                    _logger.LogInformation("成功重连TCP设备 {DeviceId} ({Ip}:{Port})",
                        device.Id, tcpDevice.Ip, tcpDevice.Port);
                }
            }
            else if (device.PortType == PortType.Rtu && device is RtuTargetDevice rtuDevice)
            {
                if (rawClient is ModbusRtuClient rtuClient)
                {
                    await Task.Run(() =>
                        rtuClient.Connect(new SerialPort(rtuDevice.PortName, rtuDevice.BaudRate,
                            rtuDevice.Parity, rtuDevice.DataBits, rtuDevice.StopBits)),
                        cancellationToken);
                    _logger.LogInformation("成功重连RTU设备 {DeviceId} ({PortName})",
                        device.Id, rtuDevice.PortName);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重连设备 {DeviceId} 失败", device.Id);
        }
    }

    /// <summary>
    /// 获取设备健康状态
    /// </summary>
    public DeviceHealthStatus? GetDeviceHealthStatus(int deviceId)
    {
        return _deviceHealthStatus.TryGetValue(deviceId, out var status) ? status : null;
    }

    /// <summary>
    /// 获取所有设备的健康状态
    /// </summary>
    public Dictionary<int, DeviceHealthStatus> GetAllDeviceHealthStatus()
    {
        return _deviceHealthStatus.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// 手动触发设备重连
    /// </summary>
    public async Task<bool> ManualReconnectAsync(int deviceId, CancellationToken cancellationToken = default)
    {
        var device = _config.TargetDevices.FirstOrDefault(d => d.Id == deviceId);
        if (device == null)
        {
            _logger.LogWarning("未找到设备 {DeviceId}", deviceId);
            return false;
        }

        _logger.LogInformation("手动触发设备 {DeviceId} 重连", deviceId);

        try
        {
            await AttemptReconnectAsync(device, cancellationToken);

            // 立即执行一次健康检查
            await CheckDeviceHealthAsync(device, cancellationToken);

            var status = GetDeviceHealthStatus(deviceId);
            return status?.IsHealthy ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动重连设备 {DeviceId} 时发生错误", deviceId);
            return false;
        }
    }
}

/// <summary>
/// 设备健康状态
/// </summary>
public class DeviceHealthStatus
{
    public int DeviceId { get; set; }
    public bool IsHealthy { get; set; }
    public DateTime LastCheckTime { get; set; }
    public DateTime? LastSuccessTime { get; set; }
    public DateTime? LastFailureTime { get; set; }
    public DateTime LastReconnectAttempt { get; set; }
    public int ConsecutiveFailures { get; set; }

    /// <summary>
    /// 获取设备状态描述
    /// </summary>
    public string StatusDescription => IsHealthy ? "健康" : $"不健康 (连续失败{ConsecutiveFailures}次)";

    /// <summary>
    /// 获取最后活动时间
    /// </summary>
    public DateTime? LastActivityTime => LastSuccessTime ?? LastFailureTime;
}
