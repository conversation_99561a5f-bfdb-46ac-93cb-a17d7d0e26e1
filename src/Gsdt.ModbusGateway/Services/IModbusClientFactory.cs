using Gsdt.ModbusGateway.Models;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus客户端工厂接口
/// </summary>
public interface IModbusClientFactory
{
    /// <summary>
    /// 创建Modbus-TCP客户端
    /// </summary>
    /// <param name="device">目标设备</param>
    /// <returns>Modbus客户端</returns>
    ModbusTcpClient CreateTcpClient(TargetDevice device);
    
    /// <summary>
    /// 创建Modbus-RTU客户端
    /// </summary>
    /// <param name="device">目标设备</param>
    /// <returns>Modbus客户端</returns>
    ModbusRtuClient CreateRtuClient(TargetDevice device);
}