using System.Collections.Concurrent;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// 配置更新管理器实现
/// </summary>
public class ConfigurationUpdateManager : IConfigurationUpdateManager
{
    private readonly ILogger<ConfigurationUpdateManager> _logger;
    private readonly ConcurrentBag<IConfigurableService> _configurableServices = new();

    public ConfigurationUpdateManager(ILogger<ConfigurationUpdateManager> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 注册可配置服务
    /// </summary>
    public void RegisterConfigurableService(IConfigurableService service)
    {
        _configurableServices.Add(service);
        _logger.LogDebug("已注册可配置服务: {ServiceType}", service.GetType().Name);
    }

    /// <summary>
    /// 注销可配置服务
    /// </summary>
    public void UnregisterConfigurableService(IConfigurableService service)
    {
        // ConcurrentBag 不支持直接移除，这里记录日志即可
        _logger.LogDebug("请求注销可配置服务: {ServiceType}", service.GetType().Name);
    }

    /// <summary>
    /// 通知所有注册的服务配置已更新
    /// </summary>
    public async Task<ConfigurationUpdateResult> NotifyConfigurationChangedAsync(GatewayConfig newConfig)
    {
        var result = new ConfigurationUpdateResult();
        var tasks = new List<Task<(bool Success, string ServiceName, string? Error)>>();

        _logger.LogInformation("开始通知 {Count} 个服务配置已更新", _configurableServices.Count);

        foreach (var service in _configurableServices)
        {
            tasks.Add(UpdateServiceAsync(service, newConfig));
        }

        var results = await Task.WhenAll(tasks);

        foreach (var (success, serviceName, error) in results)
        {
            if (success)
            {
                result.SuccessCount++;
                _logger.LogInformation("服务 {ServiceName} 配置更新成功", serviceName);
            }
            else
            {
                result.FailureCount++;
                result.Errors.Add($"{serviceName}: {error}");
                _logger.LogError("服务 {ServiceName} 配置更新失败: {Error}", serviceName, error);
            }
        }

        result.IsSuccess = result.FailureCount == 0;
        
        _logger.LogInformation("配置更新完成: 成功 {Success}/{Total}, 失败 {Failure}", 
            result.SuccessCount, result.TotalCount, result.FailureCount);

        return result;
    }

    private async Task<(bool Success, string ServiceName, string? Error)> UpdateServiceAsync(
        IConfigurableService service, GatewayConfig newConfig)
    {
        var serviceName = service.GetType().Name;
        try
        {
            var success = await service.OnConfigurationChangedAsync(newConfig);
            return (success, serviceName, success ? null : "服务返回更新失败");
        }
        catch (Exception ex)
        {
            return (false, serviceName, ex.Message);
        }
    }
}

/// <summary>
/// 可配置服务接口
/// </summary>
public interface IConfigurableService
{
    /// <summary>
    /// 当配置发生变更时调用此方法
    /// </summary>
    Task<bool> OnConfigurationChangedAsync(GatewayConfig newConfig);
}

/// <summary>
/// 配置更新管理器接口
/// </summary>
public interface IConfigurationUpdateManager
{
    void RegisterConfigurableService(IConfigurableService service);
    void UnregisterConfigurableService(IConfigurableService service);
    Task<ConfigurationUpdateResult> NotifyConfigurationChangedAsync(GatewayConfig newConfig);
}

/// <summary>
/// 配置更新结果
/// </summary>
public class ConfigurationUpdateResult
{
    public bool IsSuccess { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<string> Errors { get; set; } = [];
    public int TotalCount => SuccessCount + FailureCount;
}
