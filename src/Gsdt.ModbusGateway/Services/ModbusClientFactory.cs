using System.IO.Ports;
using System.Net;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus客户端工厂实现
/// </summary>
public class ModbusClientFactory : IModbusClientFactory
{
    private readonly ILogger<ModbusClientFactory> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ModbusClientFactory(ILogger<ModbusClientFactory> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 创建Modbus-TCP客户端
    /// </summary>
    /// <param name="device">目标设备</param>
    /// <returns>Modbus客户端</returns>
    public ModbusTcpClient CreateTcpClient(TargetDevice device)
    {
        if(device is not TcpTargetDevice tcpDevice)
        {
            throw new ArgumentException("设备类型不匹配");
        }

        _logger.LogInformation("创建Modbus客户端：ID={DeviceId}, IP={IpAddress}, Port={Port}", 
            tcpDevice.Id, tcpDevice.Ip, tcpDevice.Port);
        
        var client = new ModbusTcpClient();
        
        // 设置超时
        client.Timeout = TimeSpan.FromMilliseconds(tcpDevice.Timeout);
        
        // // 连接到设备
        // try
        // {
        //     client.Connect(new IPEndPoint(IPAddress.Parse(tcpDevice.Ip), tcpDevice.Port));
        // }
        // catch (Exception ex)
        // {
        //     _logger.LogError(ex, "连接到设备失败：ID={DeviceId}, IP={IpAddress}, Port={Port}",
        //         tcpDevice.Id, tcpDevice.Ip, tcpDevice.Port);
        // }

        return client;
    }

    /// <summary>
    /// 创建Modbus-RTU客户端
    /// </summary>
    /// <param name="device"></param>
    /// <returns>Modbus客户端</returns>
    public ModbusRtuClient CreateRtuClient(TargetDevice device)
    {
        if(device is not RtuTargetDevice rtuDevice)
        {
            throw new ArgumentException("设备类型不匹配");
        }

        _logger.LogInformation("创建Modbus客户端并连接到设备：ID={DeviceId}, PortName={PortName}, BaudRate={BaudRate}", 
            rtuDevice.Id, rtuDevice.PortName, rtuDevice.BaudRate);
        
        var client = new ModbusRtuClient();
        
        // 设置超时
        client.Timeout = TimeSpan.FromMilliseconds(rtuDevice.Timeout);
        
        // 连接到设备
        try
        {
            client.Connect(new SerialPort(rtuDevice.PortName, rtuDevice.BaudRate, rtuDevice.Parity, rtuDevice.DataBits,
                rtuDevice.StopBits));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接到设备失败：ID={DeviceId}, PortName={PortName}, BaudRate={BaudRate}",
                rtuDevice.Id, rtuDevice.PortName, rtuDevice.BaudRate);
        }
        
        return client;
    }
}
