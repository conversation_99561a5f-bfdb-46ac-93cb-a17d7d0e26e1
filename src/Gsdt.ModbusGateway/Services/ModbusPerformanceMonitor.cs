using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus性能监控服务
/// 监控请求排队时间、设备响应时间、错误率等指标
/// </summary>
public class ModbusPerformanceMonitor : IDisposable
{
    private readonly ILogger<ModbusPerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<int, DeviceMetrics> _deviceMetrics = new();
    private readonly ConcurrentDictionary<string, RequestMetrics> _activeRequests = new();
    private readonly Timer _reportTimer;
    private bool _disposed = false;

    public ModbusPerformanceMonitor(ILogger<ModbusPerformanceMonitor> logger)
    {
        _logger = logger;
        // 每30秒输出一次性能报告
        _reportTimer = new Timer(ReportMetrics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    /// <summary>
    /// 开始监控请求
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <param name="functionCode">功能码</param>
    /// <returns>请求ID，用于后续跟踪</returns>
    public string StartRequest(int deviceId, byte functionCode)
    {
        var requestId = Guid.NewGuid().ToString("N")[..8]; // 使用短ID
        var metrics = new RequestMetrics
        {
            DeviceId = deviceId,
            FunctionCode = functionCode,
            StartTime = DateTime.UtcNow,
            QueueStartTime = DateTime.UtcNow
        };

        _activeRequests[requestId] = metrics;

        // 更新设备指标
        var deviceMetrics = _deviceMetrics.GetOrAdd(deviceId, _ => new DeviceMetrics(deviceId));
        Interlocked.Increment(ref deviceMetrics.TotalRequests);
        Interlocked.Increment(ref deviceMetrics.ActiveRequests);

        _logger.LogDebug("开始监控请求: RequestId={RequestId}, DeviceId={DeviceId}, FunctionCode={FunctionCode}",
            requestId, deviceId, functionCode);

        return requestId;
    }

    /// <summary>
    /// 标记请求开始执行（离开队列）
    /// </summary>
    /// <param name="requestId">请求ID</param>
    public void MarkRequestStartExecution(string requestId)
    {
        if (_activeRequests.TryGetValue(requestId, out var metrics))
        {
            metrics.ExecutionStartTime = DateTime.UtcNow;
            var queueTime = (metrics.ExecutionStartTime.Value - metrics.QueueStartTime).TotalMilliseconds;

            var deviceMetrics = _deviceMetrics.GetOrAdd(metrics.DeviceId, _ => new DeviceMetrics(metrics.DeviceId));
            deviceMetrics.AddQueueTime(queueTime);

            _logger.LogDebug("请求开始执行: RequestId={RequestId}, QueueTime={QueueTime}ms",
                requestId, queueTime);
        }
    }

    /// <summary>
    /// 完成请求监控
    /// </summary>
    /// <param name="requestId">请求ID</param>
    /// <param name="success">是否成功</param>
    /// <param name="errorMessage">错误消息（如果失败）</param>
    public void CompleteRequest(string requestId, bool success, string? errorMessage = null)
    {
        if (_activeRequests.TryRemove(requestId, out var metrics))
        {
            var endTime = DateTime.UtcNow;
            var totalTime = (endTime - metrics.StartTime).TotalMilliseconds;
            var executionTime = metrics.ExecutionStartTime.HasValue
                ? (endTime - metrics.ExecutionStartTime.Value).TotalMilliseconds
                : totalTime;

            var deviceMetrics = _deviceMetrics.GetOrAdd(metrics.DeviceId, _ => new DeviceMetrics(metrics.DeviceId));
            Interlocked.Decrement(ref deviceMetrics.ActiveRequests);

            if (success)
            {
                Interlocked.Increment(ref deviceMetrics.SuccessfulRequests);
                deviceMetrics.AddResponseTime(executionTime);
            }
            else
            {
                Interlocked.Increment(ref deviceMetrics.FailedRequests);
                deviceMetrics.AddError(errorMessage ?? "Unknown error");
            }

            _logger.LogDebug("完成请求监控: RequestId={RequestId}, Success={Success}, TotalTime={TotalTime}ms, ExecutionTime={ExecutionTime}ms",
                requestId, success, totalTime, executionTime);

            if (!success && !string.IsNullOrEmpty(errorMessage))
            {
                _logger.LogWarning("请求失败: RequestId={RequestId}, DeviceId={DeviceId}, Error={Error}",
                    requestId, metrics.DeviceId, errorMessage);
            }
        }
    }

    /// <summary>
    /// 获取设备性能指标
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备性能指标</returns>
    public DevicePerformanceSnapshot? GetDeviceMetrics(int deviceId)
    {
        if (_deviceMetrics.TryGetValue(deviceId, out var metrics))
        {
            return metrics.GetSnapshot();
        }
        return null;
    }

    /// <summary>
    /// 获取所有设备的性能指标
    /// </summary>
    /// <returns>所有设备的性能指标</returns>
    public Dictionary<int, DevicePerformanceSnapshot> GetAllDeviceMetrics()
    {
        var result = new Dictionary<int, DevicePerformanceSnapshot>();
        foreach (var kvp in _deviceMetrics)
        {
            result[kvp.Key] = kvp.Value.GetSnapshot();
        }
        return result;
    }

    /// <summary>
    /// 重置设备指标
    /// </summary>
    /// <param name="deviceId">设备ID，如果为null则重置所有设备</param>
    public void ResetMetrics(int? deviceId = null)
    {
        if (deviceId.HasValue)
        {
            if (_deviceMetrics.TryGetValue(deviceId.Value, out var metrics))
            {
                metrics.Reset();
                _logger.LogInformation("已重置设备 {DeviceId} 的性能指标", deviceId.Value);
            }
        }
        else
        {
            foreach (var metrics in _deviceMetrics.Values)
            {
                metrics.Reset();
            }
            _logger.LogInformation("已重置所有设备的性能指标");
        }
    }

    /// <summary>
    /// 定期输出性能报告
    /// </summary>
    private void ReportMetrics(object? state)
    {
        try
        {
            var allMetrics = GetAllDeviceMetrics();
            if (allMetrics.Count == 0)
            {
                return;
            }

            _logger.LogInformation("=== Modbus性能报告 ===");
            foreach (var kvp in allMetrics.OrderBy(x => x.Key))
            {
                var deviceId = kvp.Key;
                var metrics = kvp.Value;

                _logger.LogInformation(
                    "设备 {DeviceId}: 总请求={TotalRequests}, 成功={SuccessfulRequests}, 失败={FailedRequests}, " +
                    "活跃请求={ActiveRequests}, 成功率={SuccessRate:F1}%, " +
                    "平均响应时间={AvgResponseTime:F1}ms, 平均排队时间={AvgQueueTime:F1}ms",
                    deviceId, metrics.TotalRequests, metrics.SuccessfulRequests, metrics.FailedRequests,
                    metrics.ActiveRequests, metrics.SuccessRate, metrics.AverageResponseTime, metrics.AverageQueueTime);

                if (metrics.RecentErrors.Count > 0)
                {
                    _logger.LogWarning("设备 {DeviceId} 最近错误: {Errors}",
                        deviceId, string.Join("; ", metrics.RecentErrors.Take(3)));
                }
            }
            _logger.LogInformation("=== 性能报告结束 ===");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "输出性能报告时发生错误");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        _reportTimer?.Dispose();

        // 输出最终报告
        _logger.LogInformation("正在输出最终性能报告...");
        ReportMetrics(null);

        _disposed = true;
    }
}

/// <summary>
/// 请求指标
/// </summary>
internal class RequestMetrics
{
    public int DeviceId { get; set; }
    public byte FunctionCode { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime QueueStartTime { get; set; }
    public DateTime? ExecutionStartTime { get; set; }
}

/// <summary>
/// 设备指标
/// </summary>
internal class DeviceMetrics
{
    private readonly object _lock = new();
    private readonly Queue<double> _responseTimes = new();
    private readonly Queue<double> _queueTimes = new();
    private readonly Queue<string> _recentErrors = new();
    private const int MaxSamples = 100; // 保留最近100个样本

    public int DeviceId { get; }
    public long TotalRequests;
    public long SuccessfulRequests;
    public long FailedRequests;
    public long ActiveRequests;

    public DeviceMetrics(int deviceId)
    {
        DeviceId = deviceId;
    }

    public void AddResponseTime(double responseTimeMs)
    {
        lock (_lock)
        {
            _responseTimes.Enqueue(responseTimeMs);
            if (_responseTimes.Count > MaxSamples)
            {
                _responseTimes.Dequeue();
            }
        }
    }

    public void AddQueueTime(double queueTimeMs)
    {
        lock (_lock)
        {
            _queueTimes.Enqueue(queueTimeMs);
            if (_queueTimes.Count > MaxSamples)
            {
                _queueTimes.Dequeue();
            }
        }
    }

    public void AddError(string errorMessage)
    {
        lock (_lock)
        {
            _recentErrors.Enqueue(errorMessage);
            if (_recentErrors.Count > 10) // 保留最近10个错误
            {
                _recentErrors.Dequeue();
            }
        }
    }

    public DevicePerformanceSnapshot GetSnapshot()
    {
        lock (_lock)
        {
            return new DevicePerformanceSnapshot
            {
                DeviceId = DeviceId,
                TotalRequests = TotalRequests,
                SuccessfulRequests = SuccessfulRequests,
                FailedRequests = FailedRequests,
                ActiveRequests = ActiveRequests,
                SuccessRate = TotalRequests > 0 ? (SuccessfulRequests * 100.0 / TotalRequests) : 0,
                AverageResponseTime = _responseTimes.Count > 0 ? _responseTimes.Average() : 0,
                AverageQueueTime = _queueTimes.Count > 0 ? _queueTimes.Average() : 0,
                RecentErrors = _recentErrors.ToList()
            };
        }
    }

    public void Reset()
    {
        lock (_lock)
        {
            TotalRequests = 0;
            SuccessfulRequests = 0;
            FailedRequests = 0;
            // 不重置ActiveRequests，因为可能有正在进行的请求
            _responseTimes.Clear();
            _queueTimes.Clear();
            _recentErrors.Clear();
        }
    }
}

/// <summary>
/// 设备性能快照
/// </summary>
public class DevicePerformanceSnapshot
{
    public int DeviceId { get; set; }
    public long TotalRequests { get; set; }
    public long SuccessfulRequests { get; set; }
    public long FailedRequests { get; set; }
    public long ActiveRequests { get; set; }
    public double SuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public double AverageQueueTime { get; set; }
    public List<string> RecentErrors { get; set; } = new();
}
