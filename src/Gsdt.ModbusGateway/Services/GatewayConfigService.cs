using System.Text.Json;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// 网关配置服务实现
/// </summary>
public class GatewayConfigService : IGatewayConfigService, IDisposable
{
    private readonly ILogger<GatewayConfigService> _logger;
    private readonly IConfiguration _configuration;
    private readonly AppConfig _appConfig;
    private readonly IConfigValidationService _configValidationService;
    private readonly IConfigurationUpdateManager _updateManager;
    private GatewayConfig _currentConfig;
    private FileSystemWatcher? _fileWatcher;
    private readonly object _configLock = new();
    private bool _disposed;

    /// <summary>
    /// 配置变更事件
    /// </summary>
    public event EventHandler<GatewayConfig>? ConfigChanged;

    public GatewayConfigService(
        ILogger<GatewayConfigService> logger,
        IConfiguration configuration,
        IOptions<AppConfig> appConfig,
        IConfigValidationService configValidationService,
        IConfigurationUpdateManager updateManager)
    {
        _logger = logger;
        _configuration = configuration;
        _appConfig = appConfig.Value;
        _configValidationService = configValidationService;
        _updateManager = updateManager;
        
        // 初始加载配置
        _currentConfig = LoadGatewayConfig();
        
        // 启动文件监控（如果启用）
        if (_appConfig.EnableConfigReload && !string.IsNullOrEmpty(_appConfig.GatewayConfigFile))
        {
            StartFileWatcher();
        }
    }

    /// <summary>
    /// 获取当前网关配置
    /// </summary>
    public GatewayConfig GetGatewayConfig()
    {
        lock (_configLock)
        {
            return _currentConfig;
        }
    }

    /// <summary>
    /// 重新加载网关配置
    /// </summary>
    public async Task<bool> ReloadConfigAsync()
    {
        try
        {
            var newConfig = await Task.Run(() => LoadGatewayConfig());

            lock (_configLock)
            {
                _currentConfig = newConfig;
            }

            _logger.LogInformation("网关配置已重新加载");
            ConfigChanged?.Invoke(this, newConfig);

            // 通知所有可配置服务
            var updateResult = await _updateManager.NotifyConfigurationChangedAsync(newConfig);
            if (!updateResult.IsSuccess)
            {
                _logger.LogWarning("部分服务配置更新失败: {Errors}", string.Join("; ", updateResult.Errors));
            }
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重新加载网关配置时发生错误");
            return false;
        }
    }

    /// <summary>
    /// 加载网关配置
    /// </summary>
    private GatewayConfig LoadGatewayConfig()
    {
        GatewayConfig config;

        if (!string.IsNullOrEmpty(_appConfig.GatewayConfigFile))
        {
            // 从外部文件加载配置
            config = LoadConfigFromFile(_appConfig.GatewayConfigFile);
            _logger.LogInformation("从文件加载网关配置: {ConfigFile}", _appConfig.GatewayConfigFile);
        }
        else
        {
            // 从appsettings.json加载配置（向后兼容）
            config = LoadConfigFromAppSettings();
            _logger.LogInformation("从appsettings.json加载网关配置");
        }

        // 验证配置
        _configValidationService.ValidateConfig(config);
        
        return config;
    }

    /// <summary>
    /// 从外部文件加载配置
    /// </summary>
    private GatewayConfig LoadConfigFromFile(string configFilePath)
    {
        var fullPath = Path.IsPathRooted(configFilePath) 
            ? configFilePath 
            : Path.Combine(AppContext.BaseDirectory, configFilePath);

        if (!File.Exists(fullPath))
        {
            throw new FileNotFoundException($"网关配置文件不存在: {fullPath}");
        }

        var jsonContent = File.ReadAllText(fullPath);
        var config = JsonSerializer.Deserialize(jsonContent, AppJsonContext.Default.GatewayConfig);

        if (config == null)
        {
            throw new InvalidOperationException($"无法解析网关配置文件: {fullPath}");
        }

        return config;
    }

    /// <summary>
    /// 从appsettings.json加载配置
    /// </summary>
    private GatewayConfig LoadConfigFromAppSettings()
    {
        var gatewayConfigSection = _configuration.GetSection("GatewayConfig");
        if (!gatewayConfigSection.Exists())
        {
            throw new InvalidOperationException("未找到GatewayConfig配置节");
        }

        var config = new GatewayConfig
        {
            TcpTargetDevices = gatewayConfigSection.GetSection("TcpTargetDevices").Get<List<TcpTargetDevice>>() ?? [],
            RtuTargetDevices = gatewayConfigSection.GetSection("RtuTargetDevices").Get<List<RtuTargetDevice>>() ?? [],
            Routes = gatewayConfigSection.GetSection("Routes").Get<List<RouteConfig>>() ?? []
        };

        return config;
    }

    /// <summary>
    /// 启动文件监控
    /// </summary>
    private void StartFileWatcher()
    {
        if (string.IsNullOrEmpty(_appConfig.GatewayConfigFile))
            return;

        var fullPath = Path.IsPathRooted(_appConfig.GatewayConfigFile) 
            ? _appConfig.GatewayConfigFile 
            : Path.Combine(AppContext.BaseDirectory, _appConfig.GatewayConfigFile);

        var directory = Path.GetDirectoryName(fullPath);
        var fileName = Path.GetFileName(fullPath);

        if (string.IsNullOrEmpty(directory) || string.IsNullOrEmpty(fileName))
            return;

        _fileWatcher = new FileSystemWatcher(directory, fileName)
        {
            NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
            EnableRaisingEvents = true
        };

        _fileWatcher.Changed += async (sender, e) =>
        {
            // 延迟一下，避免文件正在写入时读取
            await Task.Delay(500);
            await ReloadConfigAsync();
        };

        _logger.LogInformation("已启动配置文件监控: {ConfigFile}", fullPath);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (_disposed)
            return;

        _fileWatcher?.Dispose();
        _disposed = true;
    }
}
