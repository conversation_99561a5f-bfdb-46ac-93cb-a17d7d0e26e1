using Gsdt.ModbusGateway.Models;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// 网关配置服务接口
/// </summary>
public interface IGatewayConfigService
{
    /// <summary>
    /// 获取当前网关配置
    /// </summary>
    /// <returns>网关配置</returns>
    GatewayConfig GetGatewayConfig();

    /// <summary>
    /// 重新加载网关配置
    /// </summary>
    /// <returns>是否成功重新加载</returns>
    Task<bool> ReloadConfigAsync();

    /// <summary>
    /// 配置变更事件
    /// </summary>
    event EventHandler<GatewayConfig>? ConfigChanged;
}
