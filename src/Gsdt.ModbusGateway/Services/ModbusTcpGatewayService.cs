using System.Collections.Concurrent;
using System.Net;
using Gsdt.ModbusGateway.Models;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Gsdt.ModbusGateway.Services;

/// <summary>
/// Modbus TCP 网关服务，使用 NModbus 库实现
/// 作为 Modbus TCP 服务器监听外部连接，并将请求转发到目标设备
/// </summary>
public class ModbusTcpGatewayService : IHostedService, IDisposable
{
    private readonly ILogger<ModbusTcpGatewayService> _logger;
    private readonly GatewayConfig _config;
    private readonly IModbusClientFactory _clientFactory;
    private readonly IModbusRequestHandler _modbusRequestHandler;
    private readonly ThreadSafeModbusClientPool _clientPool;
    private readonly CancellationTokenSource _stoppingCts = new();
    private ModbusTcpServer? _modbusTcpServer;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="config">网关配置</param>
    /// <param name="clientFactory">Modbus客户端工厂</param>
    /// <param name="modbusRequestHandler">modbus消息处理</param>
    /// <param name="clientPool">线程安全的Modbus客户端连接池</param>
    public ModbusTcpGatewayService(
        ILogger<ModbusTcpGatewayService> logger,
        IOptions<GatewayConfig> config,
        IModbusClientFactory clientFactory,
        IModbusRequestHandler modbusRequestHandler,
        ThreadSafeModbusClientPool clientPool)
    {
        _logger = logger;
        _config = config.Value;
        _clientFactory = clientFactory;
        _modbusRequestHandler = modbusRequestHandler;
        _clientPool = clientPool;
    }

    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("正在启动Modbus TCP网关服务...");

        try
        {
            // 初始化所有目标设备的Modbus客户端并注册到线程安全连接池
            foreach (var device in _config.TargetDevices)
            {
                try
                {
                    ModbusClient client;
                    switch (device.PortType)
                    {
                        case PortType.Tcp:
                            client = _clientFactory.CreateTcpClient(device);
                            break;
                        case PortType.Rtu:
                            client = _clientFactory.CreateRtuClient(device);
                            break;
                        default:
                            _logger.LogWarning("不支持的端口类型: {PortType}, 设备ID: {DeviceId}", device.PortType, device.Id);
                            continue;
                    }

                    // 注册到线程安全连接池
                    _clientPool.RegisterClient(device.Id, client);
                    _logger.LogInformation("已注册设备 {DeviceId} 到线程安全连接池", device.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "初始化设备 {DeviceId} 的Modbus客户端时发生错误", device.Id);
                }
            }

            // 启动TCP监听器
            foreach (var listenPort in _config.ListenPorts)
            {
                _modbusTcpServer = new ModbusTcpServer(IPAddress.Any, listenPort, _modbusRequestHandler, _logger, _config, _clientPool, _stoppingCts);
                _modbusTcpServer.Start();

                _logger.LogInformation("Modbus TCP网关已启动，监听端口: {Port}", listenPort);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动Modbus TCP网关服务时发生错误");
            throw;
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    public async Task StopAsync(CancellationToken cancellationToken)
    {
        if (_modbusTcpServer == null)
        {
            return;
        }

        try
        {
            _logger.LogInformation("正在停止Modbus TCP网关服务...");

            await _stoppingCts.CancelAsync();

            // 停止TCP监听器
            _modbusTcpServer.Stop();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止Modbus TCP网关服务时发生错误");
        }

        _logger.LogInformation("Modbus TCP网关服务已停止");
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        _stoppingCts.Cancel();

        // 释放线程安全连接池资源
        try
        {
            // 获取所有已注册的设备并断开连接
            foreach (var deviceId in _clientPool.GetRegisteredDeviceIds())
            {
                var rawClient = _clientPool.GetRawClient(deviceId);
                if (rawClient != null)
                {
                    try
                    {
                        if (rawClient is ModbusTcpClient { IsConnected: true } tcpClient)
                        {
                            tcpClient.Disconnect();
                            tcpClient.Dispose();
                        }
                        else if (rawClient is ModbusRtuClient { IsConnected: true } rtuClient)
                        {
                            rtuClient.Disconnect();
                            rtuClient.Dispose();
                        }
                        else
                        {
                            rawClient.Dispose();
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "断开设备 {DeviceId} 的Modbus客户端连接时发生错误", deviceId);
                    }
                }
            }

            // 释放连接池
            _clientPool.Dispose();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放线程安全连接池时发生错误");
        }

        _stoppingCts.Dispose();
        GC.SuppressFinalize(this);
    }
}
