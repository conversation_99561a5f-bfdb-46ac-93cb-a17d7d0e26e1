<?xml version="1.0" encoding="utf-8" ?>

<Directives>
    <Application>
        <Assembly Name="Gsdt.ModbusGateway"  Dynamic="Required All" />
        <Assembly Name="System.Text.Json"  Dynamic="Required All" />
        <Assembly Name="System.Linq.Expressions"  Dynamic="Required All" />
        <Assembly Name="Microsoft.Extensions.Configuration.Json"  Dynamic="Required All" />
        <Assembly Name="Microsoft.Extensions.DependencyInjection"  Dynamic="Required All" />
        <Assembly Name="Microsoft.Extensions.Hosting"  Dynamic="Required All" />
        <Assembly Name="Microsoft.Extensions.Logging"  Dynamic="Required All" />
        <Assembly Name="Microsoft.Extensions.Logging.Console"  Dynamic="Required All" />
        <Assembly Name="NetCoreServer"  Dynamic="Required All" />
        <Assembly Name="NModbus"  Dynamic="Required All" />
        <Assembly Name="NModbus.Serial"  Dynamic="Required All" />
        <Assembly Name="Serilog"  Dynamic="Required All" />
        <Assembly Name="Serilog.AspNetCore"  Dynamic="Required All" />
        <Assembly Name="Serilog.Extensions.Hosting"  Dynamic="Required All" />
        <Assembly Name="Serilog.Extensions.Logging"  Dynamic="Required All" />
        <Assembly Name="Serilog.Settings.Configuration"  Dynamic="Required All" />
        <Assembly Name="Serilog.Sinks.Console"  Dynamic="Required All" />
        <Assembly Name="Serilog.Sinks.Debug"  Dynamic="Required All" />
        <Assembly Name="Serilog.Sinks.File"  Dynamic="Required All" />
    </Application>
</Directives>
