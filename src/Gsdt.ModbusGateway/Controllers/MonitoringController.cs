using Gsdt.ModbusGateway.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Gsdt.ModbusGateway.Controllers;

/// <summary>
/// 监控API控制器
/// 提供性能指标和健康状态查询接口
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class MonitoringController : ControllerBase
{
    private readonly ILogger<MonitoringController> _logger;
    private readonly ModbusPerformanceMonitor _performanceMonitor;
    private readonly ModbusConnectionHealthService _healthService;

    public MonitoringController(
        ILogger<MonitoringController> logger,
        ModbusPerformanceMonitor performanceMonitor,
        ModbusConnectionHealthService healthService)
    {
        _logger = logger;
        _performanceMonitor = performanceMonitor;
        _healthService = healthService;
    }

    /// <summary>
    /// 获取所有设备的性能指标
    /// </summary>
    /// <returns>设备性能指标</returns>
    [HttpGet("performance")]
    public ActionResult<Dictionary<int, DevicePerformanceSnapshot>> GetPerformanceMetrics()
    {
        try
        {
            var metrics = _performanceMonitor.GetAllDeviceMetrics();
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取性能指标时发生错误");
            return StatusCode(500, "获取性能指标失败");
        }
    }

    /// <summary>
    /// 获取指定设备的性能指标
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备性能指标</returns>
    [HttpGet("performance/{deviceId}")]
    public ActionResult<DevicePerformanceSnapshot> GetDevicePerformanceMetrics(int deviceId)
    {
        try
        {
            var metrics = _performanceMonitor.GetDeviceMetrics(deviceId);
            if (metrics == null)
            {
                return NotFound($"未找到设备 {deviceId} 的性能指标");
            }
            return Ok(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 性能指标时发生错误", deviceId);
            return StatusCode(500, "获取设备性能指标失败");
        }
    }

    /// <summary>
    /// 获取所有设备的健康状态
    /// </summary>
    /// <returns>设备健康状态</returns>
    [HttpGet("health")]
    public ActionResult<Dictionary<int, DeviceHealthStatus>> GetHealthStatus()
    {
        try
        {
            var healthStatus = _healthService.GetAllDeviceHealthStatus();
            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取健康状态时发生错误");
            return StatusCode(500, "获取健康状态失败");
        }
    }

    /// <summary>
    /// 获取指定设备的健康状态
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>设备健康状态</returns>
    [HttpGet("health/{deviceId}")]
    public ActionResult<DeviceHealthStatus> GetDeviceHealthStatus(int deviceId)
    {
        try
        {
            var healthStatus = _healthService.GetDeviceHealthStatus(deviceId);
            if (healthStatus == null)
            {
                return NotFound($"未找到设备 {deviceId} 的健康状态");
            }
            return Ok(healthStatus);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取设备 {DeviceId} 健康状态时发生错误", deviceId);
            return StatusCode(500, "获取设备健康状态失败");
        }
    }

    /// <summary>
    /// 手动触发设备重连
    /// </summary>
    /// <param name="deviceId">设备ID</param>
    /// <returns>重连结果</returns>
    [HttpPost("reconnect/{deviceId}")]
    public async Task<ActionResult<ReconnectResult>> ReconnectDevice(int deviceId)
    {
        try
        {
            _logger.LogInformation("收到设备 {DeviceId} 的手动重连请求", deviceId);

            var success = await _healthService.ManualReconnectAsync(deviceId, HttpContext.RequestAborted);

            return Ok(new ReconnectResult
            {
                DeviceId = deviceId,
                Success = success,
                Message = success ? "重连成功" : "重连失败",
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "手动重连设备 {DeviceId} 时发生错误", deviceId);
            return StatusCode(500, new ReconnectResult
            {
                DeviceId = deviceId,
                Success = false,
                Message = "重连过程中发生错误",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 重置性能指标
    /// </summary>
    /// <param name="deviceId">设备ID（可选，不提供则重置所有设备）</param>
    /// <returns>重置结果</returns>
    [HttpPost("performance/reset")]
    public ActionResult<ResetResult> ResetPerformanceMetrics([FromQuery] int? deviceId = null)
    {
        try
        {
            _performanceMonitor.ResetMetrics(deviceId);

            var message = deviceId.HasValue
                ? $"已重置设备 {deviceId} 的性能指标"
                : "已重置所有设备的性能指标";

            _logger.LogInformation(message);

            return Ok(new ResetResult
            {
                Success = true,
                Message = message,
                DeviceId = deviceId,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置性能指标时发生错误");
            return StatusCode(500, new ResetResult
            {
                Success = false,
                Message = "重置性能指标失败",
                Error = ex.Message,
                Timestamp = DateTime.UtcNow
            });
        }
    }

    /// <summary>
    /// 获取系统概览信息
    /// </summary>
    /// <returns>系统概览</returns>
    [HttpGet("overview")]
    public ActionResult<SystemOverview> GetSystemOverview()
    {
        try
        {
            var performanceMetrics = _performanceMonitor.GetAllDeviceMetrics();
            var healthStatus = _healthService.GetAllDeviceHealthStatus();

            var overview = new SystemOverview
            {
                TotalDevices = performanceMetrics.Count,
                HealthyDevices = healthStatus.Values.Count(h => h.IsHealthy),
                UnhealthyDevices = healthStatus.Values.Count(h => !h.IsHealthy),
                TotalRequests = performanceMetrics.Values.Sum(m => m.TotalRequests),
                TotalSuccessfulRequests = performanceMetrics.Values.Sum(m => m.SuccessfulRequests),
                TotalFailedRequests = performanceMetrics.Values.Sum(m => m.FailedRequests),
                OverallSuccessRate = performanceMetrics.Values.Sum(m => m.TotalRequests) > 0
                    ? performanceMetrics.Values.Sum(m => m.SuccessfulRequests) * 100.0 / performanceMetrics.Values.Sum(m => m.TotalRequests)
                    : 0,
                AverageResponseTime = performanceMetrics.Values.Where(m => m.AverageResponseTime > 0).Any()
                    ? performanceMetrics.Values.Where(m => m.AverageResponseTime > 0).Average(m => m.AverageResponseTime)
                    : 0,
                Timestamp = DateTime.UtcNow
            };

            return Ok(overview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取系统概览时发生错误");
            return StatusCode(500, "获取系统概览失败");
        }
    }

    /// <summary>
    /// 获取API版本信息
    /// </summary>
    /// <returns>版本信息</returns>
    [HttpGet("version")]
    public ActionResult<VersionInfo> GetVersion()
    {
        return Ok(new VersionInfo
        {
            Version = "1.0.0",
            BuildDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
            Features = new[]
            {
                "线程安全的Modbus客户端池",
                "性能监控",
                "连接健康检查",
                "自动重连",
                "监控API"
            }
        });
    }
}

/// <summary>
/// 系统概览信息
/// </summary>
public class SystemOverview
{
    public int TotalDevices { get; set; }
    public int HealthyDevices { get; set; }
    public int UnhealthyDevices { get; set; }
    public long TotalRequests { get; set; }
    public long TotalSuccessfulRequests { get; set; }
    public long TotalFailedRequests { get; set; }
    public double OverallSuccessRate { get; set; }
    public double AverageResponseTime { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 重连结果
/// </summary>
public class ReconnectResult
{
    public int DeviceId { get; set; }
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Error { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 重置结果
/// </summary>
public class ResetResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int? DeviceId { get; set; }
    public string? Error { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 版本信息
/// </summary>
public class VersionInfo
{
    public string Version { get; set; } = string.Empty;
    public string BuildDate { get; set; } = string.Empty;
    public string[] Features { get; set; } = Array.Empty<string>();
}
