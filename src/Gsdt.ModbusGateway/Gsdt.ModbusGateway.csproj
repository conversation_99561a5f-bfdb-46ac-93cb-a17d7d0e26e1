<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
         <PublishAot>true</PublishAot> 
         <JsonSerializerIsReflectionEnabledByDefault>false</JsonSerializerIsReflectionEnabledByDefault> 
         <InvariantGlobalization>true</InvariantGlobalization> 
         <TrimMode>full</TrimMode> 
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
        <PackageReference Include="System.Text.Json" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.0" />
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.0" />
        <PackageReference Include="NetCoreServer" Version="8.0.7" />
        <PackageReference Include="NModbus" Version="3.0.81" />
        <PackageReference Include="NModbus.Serial" Version="3.0.81" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    </ItemGroup>



    <ItemGroup>
        <RdXmlFile Include="rd.xml" />
    </ItemGroup>

</Project>
