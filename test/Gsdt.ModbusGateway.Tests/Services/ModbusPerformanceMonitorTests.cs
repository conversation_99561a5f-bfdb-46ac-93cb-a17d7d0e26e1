using System.Collections.Concurrent;
using System.Diagnostics;
using FluentAssertions;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// ModbusPerformanceMonitor 测试类
/// 测试性能监控的准确性和线程安全性
/// </summary>
public class ModbusPerformanceMonitorTests
{
    private readonly Mock<ILogger<ModbusPerformanceMonitor>> _loggerMock;

    public ModbusPerformanceMonitorTests()
    {
        _loggerMock = new Mock<ILogger<ModbusPerformanceMonitor>>();
    }

    /// <summary>
    /// 测试基本的请求监控功能
    /// </summary>
    [Fact]
    public void StartRequest_ShouldCreateRequestMetrics()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;
        const byte functionCode = 3;

        // 执行
        var requestId = monitor.StartRequest(deviceId, functionCode);

        // 验证
        requestId.Should().NotBeNullOrEmpty();

        var metrics = monitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();
        metrics!.TotalRequests.Should().Be(1);
        metrics.ActiveRequests.Should().Be(1);
        metrics.SuccessfulRequests.Should().Be(0);
        metrics.FailedRequests.Should().Be(0);
    }

    /// <summary>
    /// 测试成功请求的完整生命周期
    /// </summary>
    [Fact]
    public void CompleteRequest_Success_ShouldUpdateMetricsCorrectly()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;
        const byte functionCode = 3;

        // 执行
        var requestId = monitor.StartRequest(deviceId, functionCode);
        monitor.MarkRequestStartExecution(requestId);

        // 模拟一些处理时间
        Thread.Sleep(10);

        monitor.CompleteRequest(requestId, true);

        // 验证
        var metrics = monitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();
        metrics!.TotalRequests.Should().Be(1);
        metrics.SuccessfulRequests.Should().Be(1);
        metrics.FailedRequests.Should().Be(0);
        metrics.ActiveRequests.Should().Be(0);
        metrics.SuccessRate.Should().Be(100.0);
        metrics.AverageResponseTime.Should().BeGreaterThan(0);
        metrics.AverageQueueTime.Should().BeGreaterThan(0);
    }

    /// <summary>
    /// 测试失败请求的处理
    /// </summary>
    [Fact]
    public void CompleteRequest_Failure_ShouldUpdateMetricsCorrectly()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;
        const byte functionCode = 3;
        const string errorMessage = "测试错误";

        // 执行
        var requestId = monitor.StartRequest(deviceId, functionCode);
        monitor.MarkRequestStartExecution(requestId);
        monitor.CompleteRequest(requestId, false, errorMessage);

        // 验证
        var metrics = monitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();
        metrics!.TotalRequests.Should().Be(1);
        metrics.SuccessfulRequests.Should().Be(0);
        metrics.FailedRequests.Should().Be(1);
        metrics.ActiveRequests.Should().Be(0);
        metrics.SuccessRate.Should().Be(0.0);
        metrics.RecentErrors.Should().Contain(errorMessage);
    }

    /// <summary>
    /// 测试多设备的指标分离
    /// </summary>
    [Fact]
    public void MultipleDevices_ShouldMaintainSeparateMetrics()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int device1 = 1;
        const int device2 = 2;

        // 执行
        // 设备1：2个成功请求
        var request1_1 = monitor.StartRequest(device1, 3);
        var request1_2 = monitor.StartRequest(device1, 3);
        monitor.CompleteRequest(request1_1, true);
        monitor.CompleteRequest(request1_2, true);

        // 设备2：1个成功，1个失败
        var request2_1 = monitor.StartRequest(device2, 3);
        var request2_2 = monitor.StartRequest(device2, 3);
        monitor.CompleteRequest(request2_1, true);
        monitor.CompleteRequest(request2_2, false, "错误");

        // 验证
        var metrics1 = monitor.GetDeviceMetrics(device1);
        metrics1.Should().NotBeNull();
        metrics1!.TotalRequests.Should().Be(2);
        metrics1.SuccessfulRequests.Should().Be(2);
        metrics1.SuccessRate.Should().Be(100.0);

        var metrics2 = monitor.GetDeviceMetrics(device2);
        metrics2.Should().NotBeNull();
        metrics2!.TotalRequests.Should().Be(2);
        metrics2.SuccessfulRequests.Should().Be(1);
        metrics2.FailedRequests.Should().Be(1);
        metrics2.SuccessRate.Should().Be(50.0);
    }

    /// <summary>
    /// 测试并发请求监控的线程安全性
    /// </summary>
    [Fact]
    public async Task ConcurrentRequests_ShouldBeThreadSafe()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;
        const int concurrentRequests = 50;

        var exceptions = new ConcurrentBag<Exception>();
        var requestIds = new ConcurrentBag<string>();

        // 执行
        var tasks = Enumerable.Range(0, concurrentRequests).Select(i =>
            Task.Run(() =>
            {
                try
                {
                    var requestId = monitor.StartRequest(deviceId, 3);
                    requestIds.Add(requestId);

                    monitor.MarkRequestStartExecution(requestId);

                    // 模拟一些处理时间
                    Thread.Sleep(Random.Shared.Next(1, 10));

                    var success = i % 10 != 0; // 10%失败率
                    monitor.CompleteRequest(requestId, success, success ? null : "测试错误");
                }
                catch (Exception ex)
                {
                    exceptions.Add(ex);
                }
            })).ToArray();

        await Task.WhenAll(tasks);

        // 验证
        exceptions.Should().BeEmpty("并发请求监控不应该产生异常");
        requestIds.Should().HaveCount(concurrentRequests);
        requestIds.Distinct().Should().HaveCount(concurrentRequests, "所有请求ID应该是唯一的");

        var metrics = monitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();
        metrics!.TotalRequests.Should().Be(concurrentRequests);
        metrics.ActiveRequests.Should().Be(0, "所有请求都应该完成");
        metrics.SuccessfulRequests.Should().BeGreaterThan(0);
        metrics.FailedRequests.Should().BeGreaterThan(0);
        (metrics.SuccessfulRequests + metrics.FailedRequests).Should().Be(concurrentRequests);
    }

    /// <summary>
    /// 测试指标重置功能
    /// </summary>
    [Fact]
    public void ResetMetrics_ShouldClearMetricsCorrectly()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;

        // 创建一些指标
        var requestId = monitor.StartRequest(deviceId, 3);
        monitor.CompleteRequest(requestId, false, "测试错误");

        var metricsBeforeReset = monitor.GetDeviceMetrics(deviceId);
        metricsBeforeReset!.TotalRequests.Should().Be(1);

        // 执行
        monitor.ResetMetrics(deviceId);

        // 验证
        var metricsAfterReset = monitor.GetDeviceMetrics(deviceId);
        metricsAfterReset.Should().NotBeNull();
        metricsAfterReset!.TotalRequests.Should().Be(0);
        metricsAfterReset.SuccessfulRequests.Should().Be(0);
        metricsAfterReset.FailedRequests.Should().Be(0);
        metricsAfterReset.RecentErrors.Should().BeEmpty();
    }

    /// <summary>
    /// 测试获取所有设备指标
    /// </summary>
    [Fact]
    public void GetAllDeviceMetrics_ShouldReturnAllDevices()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        var deviceIds = new[] { 1, 2, 3, 5, 10 };

        // 为每个设备创建一些指标
        foreach (var deviceId in deviceIds)
        {
            var requestId = monitor.StartRequest(deviceId, 3);
            monitor.CompleteRequest(requestId, true);
        }

        // 执行
        var allMetrics = monitor.GetAllDeviceMetrics();

        // 验证
        allMetrics.Should().HaveCount(deviceIds.Length);
        foreach (var deviceId in deviceIds)
        {
            allMetrics.Should().ContainKey(deviceId);
            allMetrics[deviceId].TotalRequests.Should().Be(1);
        }
    }

    /// <summary>
    /// 测试性能监控的准确性
    /// </summary>
    [Fact]
    public async Task PerformanceAccuracy_ShouldMeasureTimesCorrectly()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;
        const int expectedQueueTime = 50;
        const int expectedExecutionTime = 100;

        // 执行
        var requestId = monitor.StartRequest(deviceId, 3);

        // 模拟排队时间
        await Task.Delay(expectedQueueTime);
        monitor.MarkRequestStartExecution(requestId);

        // 模拟执行时间
        await Task.Delay(expectedExecutionTime);
        monitor.CompleteRequest(requestId, true);

        // 验证
        var metrics = monitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();

        // 允许一定的时间误差（±20ms）
        metrics!.AverageQueueTime.Should().BeInRange(expectedQueueTime - 20, expectedQueueTime + 20);
        metrics.AverageResponseTime.Should().BeInRange(expectedExecutionTime - 20, expectedExecutionTime + 20);
    }

    /// <summary>
    /// 测试错误记录的限制
    /// </summary>
    [Fact]
    public void ErrorRecording_ShouldLimitRecentErrors()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceId = 1;
        const int errorCount = 15; // 超过最大错误记录数(10)

        // 执行
        for (int i = 0; i < errorCount; i++)
        {
            var requestId = monitor.StartRequest(deviceId, 3);
            monitor.CompleteRequest(requestId, false, $"错误 {i}");
        }

        // 验证
        var metrics = monitor.GetDeviceMetrics(deviceId);
        metrics.Should().NotBeNull();
        metrics!.FailedRequests.Should().Be(errorCount);
        (metrics.RecentErrors.Count <= 10).Should().BeTrue("应该限制最近错误的数量");

        // 验证保留的是最新的错误
        metrics.RecentErrors.Should().Contain($"错误 {errorCount - 1}");
    }

    /// <summary>
    /// 压力测试：大量并发操作
    /// </summary>
    [Fact]
    public async Task StressTest_HighConcurrency_ShouldMaintainAccuracy()
    {
        // 准备
        var monitor = new ModbusPerformanceMonitor(_loggerMock.Object);
        const int deviceCount = 10;
        const int requestsPerDevice = 100;
        const int totalRequests = deviceCount * requestsPerDevice;

        var exceptions = new ConcurrentBag<Exception>();
        var completedRequests = new ConcurrentBag<int>();
        var stopwatch = Stopwatch.StartNew();

        // 执行
        var tasks = Enumerable.Range(1, deviceCount).SelectMany(deviceId =>
            Enumerable.Range(0, requestsPerDevice).Select(requestIndex =>
                Task.Run(() =>
                {
                    try
                    {
                        var requestId = monitor.StartRequest(deviceId, 3);
                        monitor.MarkRequestStartExecution(requestId);

                        // 模拟一些处理时间
                        Thread.Sleep(Random.Shared.Next(1, 5));

                        var success = requestIndex % 20 != 0; // 5%失败率
                        monitor.CompleteRequest(requestId, success, success ? null : "压力测试错误");

                        completedRequests.Add(deviceId * 1000 + requestIndex);
                    }
                    catch (Exception ex)
                    {
                        exceptions.Add(ex);
                    }
                }))).ToArray();

        await Task.WhenAll(tasks);
        stopwatch.Stop();

        // 验证
        exceptions.Should().BeEmpty("压力测试不应该产生异常");
        completedRequests.Should().HaveCount(totalRequests);

        var allMetrics = monitor.GetAllDeviceMetrics();
        allMetrics.Should().HaveCount(deviceCount);

        var totalRecordedRequests = allMetrics.Values.Sum(m => m.TotalRequests);
        totalRecordedRequests.Should().Be(totalRequests, "应该记录所有请求");

        var totalSuccessful = allMetrics.Values.Sum(m => m.SuccessfulRequests);
        var totalFailed = allMetrics.Values.Sum(m => m.FailedRequests);
        (totalSuccessful + totalFailed).Should().Be(totalRequests, "成功和失败的请求总数应该等于总请求数");

        // 验证所有设备都没有活跃请求
        foreach (var metrics in allMetrics.Values)
        {
            metrics.ActiveRequests.Should().Be(0, "所有请求都应该完成");
        }

        // 性能验证
        var averageTimePerRequest = stopwatch.ElapsedMilliseconds / (double)totalRequests;
        averageTimePerRequest.Should().BeLessThan(20, "平均每个请求的处理时间应该在合理范围内");
    }
}
