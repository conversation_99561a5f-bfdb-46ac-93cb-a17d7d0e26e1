using System.Reflection;
using FluentAssertions;
using Gsdt.ModbusGateway.Models;
using Xunit;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// ModbusTcpServer 测试类
/// 主要测试路由转发功能
/// </summary>
public class ModbusTcpServerTests
{
    /// <summary>
    /// 测试ModifyRequest方法修改从机地址
    /// </summary>
    [Fact]
    public void ModifyRequest_ShouldChangeSlaveId()
    {
        // 准备
        byte originalSlaveId = 1;
        ushort originalStartAddress = 0;
        ushort originalQuantity = 10;

        // 创建原始请求
        var originalRequest = CreateModbusTcpRequest(originalSlaveId, 3, originalStartAddress, originalQuantity);

        // 新的参数
        ushort newSlaveId = 2;
        ushort newStartAddress = originalStartAddress;
        ushort newQuantity = originalQuantity;

        // 执行 - 通过反射调用私有静态方法
        var modifiedRequest = InvokeModifyRequest(originalRequest, newSlaveId, newStartAddress, newQuantity);

        // 验证
        modifiedRequest.Should().NotBeNull();
        modifiedRequest.Length.Should().Be(originalRequest.Length);

        // 验证从机地址已修改
        modifiedRequest[6].Should().Be((byte)newSlaveId);

        // 验证其他字段未变
        modifiedRequest[7].Should().Be(originalRequest[7]); // 功能码
        modifiedRequest[8].Should().Be(originalRequest[8]); // 起始地址高字节
        modifiedRequest[9].Should().Be(originalRequest[9]); // 起始地址低字节
        modifiedRequest[10].Should().Be(originalRequest[10]); // 数量高字节
        modifiedRequest[11].Should().Be(originalRequest[11]); // 数量低字节
    }

    /// <summary>
    /// 测试ModifyRequest方法修改起始地址
    /// </summary>
    [Fact]
    public void ModifyRequest_ShouldChangeStartAddress()
    {
        // 准备
        byte originalSlaveId = 1;
        ushort originalStartAddress = 0;
        ushort originalQuantity = 10;

        // 创建原始请求
        var originalRequest = CreateModbusTcpRequest(originalSlaveId, 3, originalStartAddress, originalQuantity);

        // 新的参数
        ushort newSlaveId = originalSlaveId;
        ushort newStartAddress = 100; // 新的起始地址
        ushort newQuantity = originalQuantity;

        // 执行 - 通过反射调用私有静态方法
        var modifiedRequest = InvokeModifyRequest(originalRequest, newSlaveId, newStartAddress, newQuantity);

        // 验证
        modifiedRequest.Should().NotBeNull();
        modifiedRequest.Length.Should().Be(originalRequest.Length);

        // 验证起始地址已修改
        modifiedRequest[8].Should().Be((byte)(newStartAddress >> 8)); // 起始地址高字节
        modifiedRequest[9].Should().Be((byte)(newStartAddress & 0xFF)); // 起始地址低字节

        // 验证其他字段未变
        modifiedRequest[6].Should().Be(originalRequest[6]); // 从机地址
        modifiedRequest[7].Should().Be(originalRequest[7]); // 功能码
        modifiedRequest[10].Should().Be(originalRequest[10]); // 数量高字节
        modifiedRequest[11].Should().Be(originalRequest[11]); // 数量低字节
    }

    /// <summary>
    /// 测试ModifyRequest方法修改寄存器数量
    /// </summary>
    [Fact]
    public void ModifyRequest_ShouldChangeQuantity()
    {
        // 准备
        byte originalSlaveId = 1;
        ushort originalStartAddress = 0;
        ushort originalQuantity = 10;

        // 创建原始请求
        var originalRequest = CreateModbusTcpRequest(originalSlaveId, 3, originalStartAddress, originalQuantity);

        // 新的参数
        ushort newSlaveId = originalSlaveId;
        ushort newStartAddress = originalStartAddress;
        ushort newQuantity = 20; // 新的寄存器数量

        // 执行 - 通过反射调用私有静态方法
        var modifiedRequest = InvokeModifyRequest(originalRequest, newSlaveId, newStartAddress, newQuantity);

        // 验证
        modifiedRequest.Should().NotBeNull();
        modifiedRequest.Length.Should().Be(originalRequest.Length);

        // 验证寄存器数量已修改
        modifiedRequest[10].Should().Be((byte)(newQuantity >> 8)); // 数量高字节
        modifiedRequest[11].Should().Be((byte)(newQuantity & 0xFF)); // 数量低字节

        // 验证其他字段未变
        modifiedRequest[6].Should().Be(originalRequest[6]); // 从机地址
        modifiedRequest[7].Should().Be(originalRequest[7]); // 功能码
        modifiedRequest[8].Should().Be(originalRequest[8]); // 起始地址高字节
        modifiedRequest[9].Should().Be(originalRequest[9]); // 起始地址低字节
    }

    /// <summary>
    /// 测试ModifyRequest方法同时修改所有参数
    /// </summary>
    [Fact]
    public void ModifyRequest_ShouldChangeAllParameters()
    {
        // 准备
        byte originalSlaveId = 1;
        ushort originalStartAddress = 0;
        ushort originalQuantity = 10;

        // 创建原始请求
        var originalRequest = CreateModbusTcpRequest(originalSlaveId, 3, originalStartAddress, originalQuantity);

        // 新的参数
        ushort newSlaveId = 2;
        ushort newStartAddress = 100;
        ushort newQuantity = 20;

        // 执行 - 通过反射调用私有静态方法
        var modifiedRequest = InvokeModifyRequest(originalRequest, newSlaveId, newStartAddress, newQuantity);

        // 验证
        modifiedRequest.Should().NotBeNull();
        modifiedRequest.Length.Should().Be(originalRequest.Length);

        // 验证所有参数已修改
        modifiedRequest[6].Should().Be((byte)newSlaveId); // 从机地址
        modifiedRequest[8].Should().Be((byte)(newStartAddress >> 8)); // 起始地址高字节
        modifiedRequest[9].Should().Be((byte)(newStartAddress & 0xFF)); // 起始地址低字节
        modifiedRequest[10].Should().Be((byte)(newQuantity >> 8)); // 数量高字节
        modifiedRequest[11].Should().Be((byte)(newQuantity & 0xFF)); // 数量低字节

        // 验证功能码未变
        modifiedRequest[7].Should().Be(originalRequest[7]); // 功能码
    }

    /// <summary>
    /// 测试ModifyRequest方法处理不同功能码的情况
    /// </summary>
    [Theory]
    [InlineData(1)] // 读取线圈
    [InlineData(2)] // 读取离散输入
    [InlineData(3)] // 读取保持寄存器
    [InlineData(4)] // 读取输入寄存器
    [InlineData(5)] // 写入单个线圈
    [InlineData(6)] // 写入单个寄存器
    [InlineData(15)] // 写入多个线圈
    [InlineData(16)] // 写入多个寄存器
    public void ModifyRequest_ShouldWorkWithDifferentFunctionCodes(byte functionCode)
    {
        // 准备
        byte originalSlaveId = 1;
        ushort originalStartAddress = 0;
        ushort originalQuantity = 10;

        // 创建原始请求
        var originalRequest = CreateModbusTcpRequest(originalSlaveId, functionCode, originalStartAddress, originalQuantity);

        // 新的参数
        ushort newSlaveId = 2;
        ushort newStartAddress = 100;
        ushort newQuantity = 20;

        // 执行 - 通过反射调用私有静态方法
        var modifiedRequest = InvokeModifyRequest(originalRequest, newSlaveId, newStartAddress, newQuantity);

        // 验证
        modifiedRequest.Should().NotBeNull();
        modifiedRequest.Length.Should().Be(originalRequest.Length);

        // 验证参数已修改
        modifiedRequest[6].Should().Be((byte)newSlaveId); // 从机地址
        modifiedRequest[8].Should().Be((byte)(newStartAddress >> 8)); // 起始地址高字节
        modifiedRequest[9].Should().Be((byte)(newStartAddress & 0xFF)); // 起始地址低字节

        if (functionCode < 5)
        {
            modifiedRequest[10].Should().Be((byte)(newQuantity >> 8)); // 数量高字节
            modifiedRequest[11].Should().Be((byte)(newQuantity & 0xFF)); // 数量低字节
        }

        // 验证功能码未变
        modifiedRequest[7].Should().Be(functionCode); // 功能码
    }

    /// <summary>
    /// 创建Modbus TCP请求
    /// </summary>
    private static byte[] CreateModbusTcpRequest(byte slaveId, byte functionCode, ushort startAddress, ushort quantity)
    {
        var request = new byte[12]; // MBAP(6) + 从站ID(1) + 功能码(1) + 起始地址(2) + 数量(2)

        // 设置事务ID、协议ID
        request[0] = 0x00; // 事务ID高字节
        request[1] = 0x01; // 事务ID低字节
        request[2] = 0x00; // 协议ID高字节
        request[3] = 0x00; // 协议ID低字节
        request[4] = 0x00; // 长度高字节
        request[5] = 0x06; // 长度低字节

        // 设置从站ID和功能码
        request[6] = slaveId;
        request[7] = functionCode;

        // 设置起始地址
        request[8] = (byte)(startAddress >> 8);
        request[9] = (byte)(startAddress & 0xFF);

        // 设置数量
        request[10] = (byte)(quantity >> 8);
        request[11] = (byte)(quantity & 0xFF);

        return request;
    }

    /// <summary>
    /// 通过反射调用ModifyRequest方法
    /// </summary>
    private static byte[] InvokeModifyRequest(byte[] originalRequest, ushort slaveId, ushort newStartAddress, ushort newQuantity)
    {
        // 获取ModbusTcpSession类型
        var sessionType = typeof(ModbusTcpServer).Assembly.GetType("Gsdt.ModbusGateway.Models.ModbusTcpSession");

        // 获取ModifyRequest方法
        var method = sessionType?.GetMethod("ModifyRequest",
            BindingFlags.NonPublic | BindingFlags.Static);

        if (method == null)
        {
            throw new InvalidOperationException("ModifyRequest方法未找到");
        }

        // 调用方法
        return (byte[])method.Invoke(null, new object[] { originalRequest, slaveId, newStartAddress, newQuantity })!;
    }
}