using Gsdt.ModbusGateway.Models;
using Gsdt.ModbusGateway.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using FluentAssertions;

namespace Gsdt.ModbusGateway.Tests.Services;

/// <summary>
/// ModbusRequestHandler 测试类
/// 主要测试不同功能码的处理
/// </summary>
public class ModbusRequestHandlerTests
{
    private readonly Mock<ILogger<ModbusRequestHandler>> _loggerMock;
    private readonly TestModbusClient _client;
    private readonly ModbusRequestHandler _handler;

    public ModbusRequestHandlerTests()
    {
        _loggerMock = new Mock<ILogger<ModbusRequestHandler>>();
        _client = new TestModbusClient();
        _handler = new ModbusRequestHandler(_loggerMock.Object);
    }

    /// <summary>
    /// 测试功能码1：读取线圈
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_ReadCoils_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 10;
        
        // 创建Modbus TCP请求
        var request = CreateModbusTcpRequest(slaveId, 1, startAddress, quantity);
        
        // 设置模拟客户端返回数据
        var coilsData = new byte[] { 0x55, 0x01 }; // 0101 0101 0000 0001
        _client.SetReadCoilsResponse(coilsData);
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(11); // MBAP(6) + 从站ID(1) + 功能码(1) + 字节数(1) + 数据(2)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be(1); // 功能码
        response[8].Should().Be(2); // 字节数
        response[9].Should().Be(0x55); // 数据
        response[10].Should().Be(0x01); // 数据
        
        // 验证调用
        _client.ReadCoilsCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastStartAddress.Should().Be(startAddress);
        _client.LastQuantity.Should().Be(quantity);
    }
    
    /// <summary>
    /// 测试功能码2：读取离散输入
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_ReadDiscreteInputs_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 10;
        
        // 创建Modbus TCP请求
        var request = CreateModbusTcpRequest(slaveId, 2, startAddress, quantity);
        
        // 设置模拟客户端返回数据
        var inputsData = new byte[] { 0xAA, 0x01 }; // 1010 1010 0000 0001
        _client.SetReadDiscreteInputsResponse(inputsData);
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(11); // MBAP(6) + 从站ID(1) + 功能码(1) + 字节数(1) + 数据(2)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be(2); // 功能码
        response[8].Should().Be(2); // 字节数
        response[9].Should().Be(0xAA); // 数据
        response[10].Should().Be(0x01); // 数据
        
        // 验证调用
        _client.ReadDiscreteInputsCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastStartAddress.Should().Be(startAddress);
        _client.LastQuantity.Should().Be(quantity);
    }
    
    /// <summary>
    /// 测试功能码3：读取保持寄存器
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_ReadHoldingRegisters_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 2;
        
        // 创建Modbus TCP请求
        var request = CreateModbusTcpRequest(slaveId, 3, startAddress, quantity);
        
        // 设置模拟客户端返回数据
        var registersData = new byte[] { 0x12, 0x34, 0x56, 0x78 }; // 两个寄存器：0x1234, 0x5678
        _client.SetReadHoldingRegistersResponse(registersData);
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(13); // MBAP(6) + 从站ID(1) + 功能码(1) + 字节数(1) + 数据(4)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be(3); // 功能码
        response[8].Should().Be(4); // 字节数
        response[9].Should().Be(0x12); // 数据
        response[10].Should().Be(0x34); // 数据
        response[11].Should().Be(0x56); // 数据
        response[12].Should().Be(0x78); // 数据
        
        // 验证调用
        _client.ReadHoldingRegistersCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastStartAddress.Should().Be(startAddress);
        _client.LastQuantity.Should().Be(quantity);
    }
    
    /// <summary>
    /// 测试功能码4：读取输入寄存器
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_ReadInputRegisters_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 2;
        
        // 创建Modbus TCP请求
        var request = CreateModbusTcpRequest(slaveId, 4, startAddress, quantity);
        
        // 设置模拟客户端返回数据
        var registersData = new byte[] { 0xAB, 0xCD, 0xEF, 0x12 }; // 两个寄存器：0xABCD, 0xEF12
        _client.SetReadInputRegistersResponse(registersData);
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(13); // MBAP(6) + 从站ID(1) + 功能码(1) + 字节数(1) + 数据(4)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be(4); // 功能码
        response[8].Should().Be(4); // 字节数
        response[9].Should().Be(0xAB); // 数据
        response[10].Should().Be(0xCD); // 数据
        response[11].Should().Be(0xEF); // 数据
        response[12].Should().Be(0x12); // 数据
        
        // 验证调用
        _client.ReadInputRegistersCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastStartAddress.Should().Be(startAddress);
        _client.LastQuantity.Should().Be(quantity);
    }
    
    /// <summary>
    /// 测试功能码5：写入单个线圈
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_WriteSingleCoil_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort address = 0;
        bool value = true; // 0xFF00 表示 ON
        
        // 创建Modbus TCP请求
        var request = new byte[12]; // MBAP(6) + 从站ID(1) + 功能码(1) + 地址(2) + 值(2)
        // 设置事务ID、协议ID
        request[0] = 0x00; // 事务ID高字节
        request[1] = 0x01; // 事务ID低字节
        request[2] = 0x00; // 协议ID高字节
        request[3] = 0x00; // 协议ID低字节
        request[4] = 0x00; // 长度高字节
        request[5] = 0x06; // 长度低字节
        request[6] = slaveId; // 从站ID
        request[7] = 5; // 功能码
        request[8] = (byte)(address >> 8); // 地址高字节
        request[9] = (byte)(address & 0xFF); // 地址低字节
        request[10] = (byte)(value ? 0xFF : 0x00); // 值高字节
        request[11] = 0x00; // 值低字节
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(12); // 与请求相同
        response.Should().BeEquivalentTo(request); // 响应应与请求相同
        
        // 验证调用
        _client.WriteSingleCoilCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastAddress.Should().Be(address);
        _client.LastBoolValue.Should().Be(value);
    }
    
    /// <summary>
    /// 测试功能码6：写入单个寄存器
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_WriteSingleRegister_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort address = 0;
        ushort value = 0x1234;
        
        // 创建Modbus TCP请求
        var request = new byte[12]; // MBAP(6) + 从站ID(1) + 功能码(1) + 地址(2) + 值(2)
        // 设置事务ID、协议ID
        request[0] = 0x00; // 事务ID高字节
        request[1] = 0x01; // 事务ID低字节
        request[2] = 0x00; // 协议ID高字节
        request[3] = 0x00; // 协议ID低字节
        request[4] = 0x00; // 长度高字节
        request[5] = 0x06; // 长度低字节
        request[6] = slaveId; // 从站ID
        request[7] = 6; // 功能码
        request[8] = (byte)(address >> 8); // 地址高字节
        request[9] = (byte)(address & 0xFF); // 地址低字节
        request[10] = (byte)(value >> 8); // 值高字节
        request[11] = (byte)(value & 0xFF); // 值低字节
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(12); // 与请求相同
        response.Should().BeEquivalentTo(request); // 响应应与请求相同
        
        // 验证调用
        _client.WriteSingleRegisterCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastAddress.Should().Be(address);
        _client.LastUshortValue.Should().Be(value);
    }
    
    /// <summary>
    /// 测试功能码15：写入多个线圈
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_WriteMultipleCoils_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 10;
        
        // 创建Modbus TCP请求
        var request = new byte[15]; // MBAP(6) + 从站ID(1) + 功能码(1) + 地址(2) + 数量(2) + 字节数(1) + 数据(2)
        // 设置事务ID、协议ID
        request[0] = 0x00; // 事务ID高字节
        request[1] = 0x01; // 事务ID低字节
        request[2] = 0x00; // 协议ID高字节
        request[3] = 0x00; // 协议ID低字节
        request[4] = 0x00; // 长度高字节
        request[5] = 0x09; // 长度低字节
        request[6] = slaveId; // 从站ID
        request[7] = 15; // 功能码
        request[8] = (byte)(startAddress >> 8); // 地址高字节
        request[9] = (byte)(startAddress & 0xFF); // 地址低字节
        request[10] = (byte)(quantity >> 8); // 数量高字节
        request[11] = (byte)(quantity & 0xFF); // 数量低字节
        request[12] = 2; // 字节数
        request[13] = 0x55; // 数据
        request[14] = 0x01; // 数据
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(12); // MBAP(6) + 从站ID(1) + 功能码(1) + 地址(2) + 数量(2)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be(15); // 功能码
        response[8].Should().Be((byte)(startAddress >> 8)); // 地址高字节
        response[9].Should().Be((byte)(startAddress & 0xFF)); // 地址低字节
        response[10].Should().Be((byte)(quantity >> 8)); // 数量高字节
        response[11].Should().Be((byte)(quantity & 0xFF)); // 数量低字节
        
        // 验证调用
        _client.WriteMultipleCoilsCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastStartAddress.Should().Be(startAddress);
    }
    
    /// <summary>
    /// 测试功能码16：写入多个寄存器
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_WriteMultipleRegisters_ShouldReturnCorrectResponse()
    {
        // 准备
        byte slaveId = 1;
        ushort startAddress = 0;
        ushort quantity = 2;
        
        // 创建Modbus TCP请求
        var request = new byte[17]; // MBAP(6) + 从站ID(1) + 功能码(1) + 地址(2) + 数量(2) + 字节数(1) + 数据(4)
        // 设置事务ID、协议ID
        request[0] = 0x00; // 事务ID高字节
        request[1] = 0x01; // 事务ID低字节
        request[2] = 0x00; // 协议ID高字节
        request[3] = 0x00; // 协议ID低字节
        request[4] = 0x00; // 长度高字节
        request[5] = 0x0B; // 长度低字节
        request[6] = slaveId; // 从站ID
        request[7] = 16; // 功能码
        request[8] = (byte)(startAddress >> 8); // 地址高字节
        request[9] = (byte)(startAddress & 0xFF); // 地址低字节
        request[10] = (byte)(quantity >> 8); // 数量高字节
        request[11] = (byte)(quantity & 0xFF); // 数量低字节
        request[12] = 4; // 字节数
        request[13] = 0x12; // 数据
        request[14] = 0x34; // 数据
        request[15] = 0x56; // 数据
        request[16] = 0x78; // 数据
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(12); // MBAP(6) + 从站ID(1) + 功能码(1) + 地址(2) + 数量(2)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be(16); // 功能码
        response[8].Should().Be((byte)(startAddress >> 8)); // 地址高字节
        response[9].Should().Be((byte)(startAddress & 0xFF)); // 地址低字节
        response[10].Should().Be((byte)(quantity >> 8)); // 数量高字节
        response[11].Should().Be((byte)(quantity & 0xFF)); // 数量低字节
        
        // 验证调用
        _client.WriteMultipleRegistersCalled.Should().BeTrue();
        _client.LastSlaveId.Should().Be(slaveId);
        _client.LastStartAddress.Should().Be(startAddress);
    }
    
    /// <summary>
    /// 测试不支持的功能码
    /// </summary>
    [Fact]
    public async Task ForwardRequestAsync_UnsupportedFunctionCode_ShouldReturnExceptionResponse()
    {
        // 准备
        byte slaveId = 1;
        byte functionCode = 100; // 不支持的功能码
        ushort startAddress = 0;
        ushort quantity = 1;
        
        // 创建Modbus TCP请求
        var request = CreateModbusTcpRequest(slaveId, functionCode, startAddress, quantity);
        
        // 执行
        var response = await _handler.ForwardRequestAsync(_client, request, slaveId, CancellationToken.None);
        
        // 验证
        response.Should().NotBeNull();
        response.Length.Should().Be(9); // MBAP(6) + 从站ID(1) + 功能码(1) + 异常码(1)
        response[6].Should().Be(slaveId); // 从站ID
        response[7].Should().Be((byte)(functionCode | 0x80)); // 异常功能码
        response[8].Should().Be(1); // 异常码：非法功能
    }
    
    /// <summary>
    /// 创建Modbus TCP请求
    /// </summary>
    private static byte[] CreateModbusTcpRequest(byte slaveId, byte functionCode, ushort startAddress, ushort quantity)
    {
        var request = new byte[12]; // MBAP(6) + 从站ID(1) + 功能码(1) + 起始地址(2) + 数量(2)
        
        // 设置事务ID、协议ID
        request[0] = 0x00; // 事务ID高字节
        request[1] = 0x01; // 事务ID低字节
        request[2] = 0x00; // 协议ID高字节
        request[3] = 0x00; // 协议ID低字节
        request[4] = 0x00; // 长度高字节
        request[5] = 0x06; // 长度低字节
        
        // 设置从站ID和功能码
        request[6] = slaveId;
        request[7] = functionCode;
        
        // 设置起始地址
        request[8] = (byte)(startAddress >> 8);
        request[9] = (byte)(startAddress & 0xFF);
        
        // 设置数量
        request[10] = (byte)(quantity >> 8);
        request[11] = (byte)(quantity & 0xFF);
        
        return request;
    }
    
    /// <summary>
    /// 用于测试的ModbusClient实现
    /// </summary>
    private class TestModbusClient : ModbusClient
    {
        // 记录调用信息
        public bool ReadCoilsCalled { get; private set; }
        public bool ReadDiscreteInputsCalled { get; private set; }
        public bool ReadHoldingRegistersCalled { get; private set; }
        public bool ReadInputRegistersCalled { get; private set; }
        public bool WriteSingleCoilCalled { get; private set; }
        public bool WriteSingleRegisterCalled { get; private set; }
        public bool WriteMultipleCoilsCalled { get; private set; }
        public bool WriteMultipleRegistersCalled { get; private set; }
        
        // 记录参数
        public byte LastSlaveId { get; private set; }
        public ushort LastStartAddress { get; private set; }
        public ushort LastQuantity { get; private set; }
        public ushort LastAddress { get; private set; }
        public bool LastBoolValue { get; private set; }
        public ushort LastUshortValue { get; private set; }
        
        // 模拟返回值
        private byte[] _readCoilsResponse = Array.Empty<byte>();
        private byte[] _readDiscreteInputsResponse = Array.Empty<byte>();
        private byte[] _readHoldingRegistersResponse = Array.Empty<byte>();
        private byte[] _readInputRegistersResponse = Array.Empty<byte>();
        
        // 设置模拟返回值
        public void SetReadCoilsResponse(byte[] response) => _readCoilsResponse = response;
        public void SetReadDiscreteInputsResponse(byte[] response) => _readDiscreteInputsResponse = response;
        public void SetReadHoldingRegistersResponse(byte[] response) => _readHoldingRegistersResponse = response;
        public void SetReadInputRegistersResponse(byte[] response) => _readInputRegistersResponse = response;
        
        // 重写方法
        public override async Task<Memory<byte>> ReadCoilsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
        {
            ReadCoilsCalled = true;
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
            
            return await Task.FromResult(new Memory<byte>(_readCoilsResponse));
        }
        
        public override async Task<Memory<byte>> ReadDiscreteInputsAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
        {
            ReadDiscreteInputsCalled = true;
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
            
            return await Task.FromResult(new Memory<byte>(_readDiscreteInputsResponse));
        }
        
        public override async Task<Memory<byte>> ReadHoldingRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
        {
            ReadHoldingRegistersCalled = true;
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
            
            return await Task.FromResult(new Memory<byte>(_readHoldingRegistersResponse));
        }
        
        public override async Task<Memory<byte>> ReadInputRegistersAsync(byte slaveId, ushort startAddress, ushort quantity, CancellationToken cancellationToken)
        {
            ReadInputRegistersCalled = true;
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            LastQuantity = quantity;
            
            return await Task.FromResult(new Memory<byte>(_readInputRegistersResponse));
        }
        
        public override async Task WriteSingleCoilAsync(byte slaveId, ushort address, bool value, CancellationToken cancellationToken)
        {
            WriteSingleCoilCalled = true;
            LastSlaveId = slaveId;
            LastAddress = address;
            LastBoolValue = value;
            
            await Task.CompletedTask;
        }
        
        public override async Task WriteSingleRegisterAsync(byte slaveId, ushort address, ushort value, CancellationToken cancellationToken)
        {
            WriteSingleRegisterCalled = true;
            LastSlaveId = slaveId;
            LastAddress = address;
            LastUshortValue = value;
            
            await Task.CompletedTask;
        }
        
        public override async Task WriteMultipleCoilsAsync(byte slaveId, ushort startAddress, bool[] values, CancellationToken cancellationToken)
        {
            WriteMultipleCoilsCalled = true;
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            
            await Task.CompletedTask;
        }
        
        public override async Task WriteMultipleRegistersAsync(byte slaveId, ushort startAddress, ushort[] values, CancellationToken cancellationToken)
        {
            WriteMultipleRegistersCalled = true;
            LastSlaveId = slaveId;
            LastStartAddress = startAddress;
            
            await Task.CompletedTask;
        }
    }
}
